// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package database

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type AppUser struct {
	ID           int32  `json:"id"`
	Email        string `json:"email"`
	MobileNumber string `json:"mobile_number"`
}

type Cooperative struct {
	ID      int32  `json:"id"`
	Name    string `json:"name"`
	Address string `json:"address"`
	ApiUrl  string `json:"api_url"`
	ApiKey  string `json:"api_key"`
}

type Message struct {
	ID               int32              `json:"id"`
	PurchaseOrderID  *int32             `json:"purchase_order_id"`
	ErrorCode        *int32             `json:"error_code"`
	ErrorDescription *string            `json:"error_description"`
	Data             []byte             `json:"data"`
	CreatedAt        pgtype.Timestamptz `json:"created_at"`
}

type MilkReport struct {
	ID                 int64              `json:"id"`
	CreatedAt          pgtype.Timestamptz `json:"created_at"`
	UpdatedAt          pgtype.Timestamptz `json:"updated_at"`
	DeletedAt          pgtype.Timestamptz `json:"deleted_at"`
	Date               pgtype.Date        `json:"date"`
	Agent              string             `json:"agent"`
	UserName           *string            `json:"user_name"`
	TotalQuantityErp   pgtype.Numeric     `json:"total_quantity_erp"`
	TotalQuantityAgent pgtype.Numeric     `json:"total_quantity_agent"`
	Variance           pgtype.Numeric     `json:"variance"`
	PdfPath            *string            `json:"pdf_path"`
	ErpCompanyName     *string            `json:"erp_company_name"`
	IDCooperative      *int64             `json:"id_cooperative"`
	IDUserCooperative  *int64             `json:"id_user_cooperative"`
}

type MilkReportAudit struct {
	AuditID               int64              `json:"audit_id"`
	MilkReportID          int64              `json:"milk_report_id"`
	ChangedAt             pgtype.Timestamptz `json:"changed_at"`
	ChangeType            string             `json:"change_type"`
	OldCreatedAt          pgtype.Timestamptz `json:"old_created_at"`
	OldUpdatedAt          pgtype.Timestamptz `json:"old_updated_at"`
	OldDeletedAt          pgtype.Timestamptz `json:"old_deleted_at"`
	OldDate               pgtype.Date        `json:"old_date"`
	OldAgent              *string            `json:"old_agent"`
	OldUserName           *string            `json:"old_user_name"`
	OldTotalQuantityErp   pgtype.Numeric     `json:"old_total_quantity_erp"`
	OldTotalQuantityAgent pgtype.Numeric     `json:"old_total_quantity_agent"`
	OldVariance           pgtype.Numeric     `json:"old_variance"`
	OldPdfPath            *string            `json:"old_pdf_path"`
	OldErpCompanyName     *string            `json:"old_erp_company_name"`
	OldIDCooperative      *int64             `json:"old_id_cooperative"`
	OldIDUserCooperative  *int64             `json:"old_id_user_cooperative"`
}

type PurchaseOrder struct {
	ID              int32              `json:"id"`
	Name            string             `json:"name"`
	TotalQty        pgtype.Numeric     `json:"total_qty"`
	CustomSno       *string            `json:"custom_sno"`
	ContactMobile   *string            `json:"contact_mobile"`
	CustomAgent     *string            `json:"custom_agent"`
	Status          *string            `json:"status"`
	SupplierName    *string            `json:"supplier_name"`
	TransactionDate pgtype.Date        `json:"transaction_date"`
	Total           pgtype.Numeric     `json:"total"`
	CooperativeID   *int32             `json:"cooperative_id"`
	SupplierID      *int32             `json:"supplier_id"`
	CreatedAt       pgtype.Timestamptz `json:"created_at"`
	Company         *string            `json:"company"`
	Creation        pgtype.Timestamptz `json:"creation"`
}

type Rate struct {
	ID             int32          `json:"id"`
	Month          pgtype.Date    `json:"month"`
	BaseRate       pgtype.Numeric `json:"base_rate"`
	DeliveryMethod *string        `json:"delivery_method"`
	AgentID        *int32         `json:"agent_id"`
	CooperativeID  *int32         `json:"cooperative_id"`
	Deduction      pgtype.Numeric `json:"deduction"`
	FinalRate      pgtype.Numeric `json:"final_rate"`
}

type Role struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

type Supplier struct {
	ID            int32   `json:"id"`
	IDExternal    string  `json:"id_external"`
	Name          string  `json:"name"`
	Sno           string  `json:"sno"`
	MobileNumber  string  `json:"mobile_number"`
	CustomID      *string `json:"custom_id"`
	Location      *string `json:"location"`
	Bank          *string `json:"bank"`
	BankBranch    *string `json:"bank_branch"`
	AccountNumber *string `json:"account_number"`
	Gender        *string `json:"gender"`
	IDCooperative int64   `json:"id_cooperative"`
	IDType        int64   `json:"id_type"`
}

type SupplierType struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

type TransactionLog struct {
	ID                int32            `json:"id"`
	TotalQuantity     pgtype.Numeric   `json:"total_quantity"`
	GrandTotal        pgtype.Numeric   `json:"grand_total"`
	TransactionDate   pgtype.Timestamp `json:"transaction_date"`
	IDCooperativeUser int64            `json:"id_cooperative_user"`
	IDSupplier        int64            `json:"id_supplier"`
	IDStatus          int64            `json:"id_status"`
}

type TransactionStatus struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

type User struct {
	ID       int32  `json:"id"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type UserCooperative struct {
	ID            int32  `json:"id"`
	UserName      string `json:"user_name"`
	ExternalID    string `json:"external_id"`
	IDUser        int64  `json:"id_user"`
	IDCooperative int64  `json:"id_cooperative"`
	IDRole        int64  `json:"id_role"`
}
