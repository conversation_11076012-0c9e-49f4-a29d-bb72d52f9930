// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.26.0

package database

import (
	"context"
)

//go:generate mockgen -source=Querier.go -destination=mockQuerier.go -package=database

type Querier interface {
	AssignAgentToCooperative(ctx context.Context, arg AssignAgentToCooperativeParams) error
	CreateAgent(ctx context.Context, arg CreateAgentParams) (int32, error)
	CreateCooperative(ctx context.Context, arg CreateCooperativeParams) (int32, error)
	CreateFarmer(ctx context.Context, arg CreateFarmerParams) (int32, error)
	CreateUser(ctx context.Context, arg CreateUserParams) error
	DeleteCooperative(ctx context.Context, id int32) error
	DeleteFarmer(ctx context.Context, id int32) error
	GetCooperative(ctx context.Context, id int32) (Cooperative, error)
	GetFarmer(ctx context.Context, id int32) (Supplier, error)
	GetUserByUsername(ctx context.Context, username string) (User, error)
	ListAgents(ctx context.Context, arg ListAgentsParams) ([]AppUser, error)
	ListAllAgentsForCooperative(ctx context.Context, idCooperative int64) ([]ListAllAgentsForCooperativeRow, error)
	ListCooperatives(ctx context.Context) ([]Cooperative, error)
	ListFarmers(ctx context.Context, idCooperative int64) ([]Supplier, error)
	SearchFarmers(ctx context.Context, arg SearchFarmersParams) ([]Supplier, error)
	UpdateCooperative(ctx context.Context, arg UpdateCooperativeParams) error
	UpdateFarmer(ctx context.Context, arg UpdateFarmerParams) error
}

var _ Querier = (*Queries)(nil)
