-- Active: 1721333401368@@127.0.0.1@5432
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL
);

CREATE TABLE cooperative (
    id serial PRIMARY KEY, 
    name text not null unique, 
    address text not null, 
    api_url text not null, 
    api_key text not null
);

CREATE TABLE role (
    id bigint PRIMARY KEY, 
    name text not null
);

CREATE TABLE transaction_status (
    id bigint PRIMARY KEY, 
    name text not null
);

CREATE TABLE supplier_type (
    id bigint PRIMARY KEY, 
    name text not null
);

CREATE TABLE app_user (
    id serial PRIMARY KEY, 
    email text not null unique, 
    mobile_number text not null unique
);

CREATE TABLE user_cooperative (
    id serial PRIMARY KEY,
    user_name text not null,
    external_id text not null unique, 
    id_user bigint REFERENCES app_user not null, 
    id_cooperative bigint REFERENCES cooperative not null, 
    id_role bigint REFERENCES role not null
);

CREATE TABLE supplier (
    id serial PRIMARY KEY, 
    id_external text not null unique,
    name text not null, 
    sno text not null, 
    mobile_number text not null, 
    custom_id text, 
    location text, 
    bank text, 
    bank_branch text, 
    account_number text, 
    gender text,
    id_cooperative bigint REFERENCES cooperative not null,
    id_type bigint REFERENCES supplier_type not null
);

-- Migration: Adjust supplier constraints

-- Drop the existing constraint on id_external
ALTER TABLE supplier
DROP CONSTRAINT IF EXISTS supplier_id_external_key;

-- Add a new unique constraint on the combination of id_external and id_cooperative
ALTER TABLE supplier
ADD CONSTRAINT supplier_id_external_cooperative_key UNIQUE (id_external, id_cooperative);


CREATE TABLE transaction_log (
    id serial PRIMARY KEY, 
    total_quantity DECIMAL not null, 
    grand_total DECIMAL, 
    transaction_date TIMESTAMP not null,
    id_cooperative_user bigint REFERENCES user_cooperative not null,
    id_supplier bigint REFERENCES supplier not null,
    id_status bigint REFERENCES transaction_status not null
);



-- Drop the tables if they already exist
DROP TABLE IF EXISTS purchase_orders;
DROP TABLE IF EXISTS messages;

-- Create 'messages' table
CREATE TABLE messages (
    id SERIAL PRIMARY KEY,
    purchase_order_id INTEGER REFERENCES purchase_orders(id),
    error_code INTEGER, 
    error_description TEXT,
    data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);


-- Create 'purchase_orders' table-- Create 'purchase_orders' table
-- Updated 'purchase_orders' table
CREATE TABLE purchase_orders (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    total_qty NUMERIC(10, 2),
    custom_sno VARCHAR(10),
    contact_mobile VARCHAR(15),
    custom_agent VARCHAR(255),
    status VARCHAR(50),
    supplier_name VARCHAR(100),
    transaction_date DATE,
    total NUMERIC(10, 2),
    cooperative_id INTEGER REFERENCES cooperative(id),
    supplier_id INTEGER REFERENCES supplier(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    company VARCHAR(100),  -- New column
    creation TIMESTAMP WITH TIME ZONE  -- New column
);



CREATE TABLE milk_report (
  id bigint PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  deleted_at timestamptz,
  date date NOT NULL,
  agent text NOT NULL,
  user_name text,
  total_quantity_erp numeric,
  total_quantity_agent numeric,
  variance numeric,
  pdf_path text,
  erp_company_name text,
  id_cooperative bigint,
  id_user_cooperative bigint,
  FOREIGN KEY (id_cooperative) REFERENCES cooperative (id),
  FOREIGN KEY (id_user_cooperative) REFERENCES user_cooperative (id)
);

CREATE TABLE rates (
  id serial NOT NULL,
  month date NOT NULL,
  base_rate numeric(10, 2) NOT NULL,
  delivery_method character varying(50) NULL,
  agent_id integer NULL,
  cooperative_id integer NULL,
  deduction numeric(10, 2) NULL DEFAULT 0,
  final_rate numeric(10, 2) NULL DEFAULT (base_rate - deduction),
  CONSTRAINT rates_pkey PRIMARY KEY (id),
  FOREIGN KEY (agent_id) REFERENCES user_cooperative (id),
  FOREIGN KEY (cooperative_id) REFERENCES cooperative (id)
);

CREATE INDEX idx_milk_report_date ON milk_report USING btree (date);
CREATE INDEX idx_milk_report_agent ON milk_report USING btree (agent);

-- Trigger to update the updated_at column on modification
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_milk_report_updated_at
BEFORE UPDATE ON milk_report
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();



-- 	1.	Audit Table (milk_report_audit):
-- 	•	This table will store historical data from the milk_report table whenever a change occurs.
-- 	•	It includes a reference to the original milk_report record (milk_report_id), a timestamp of when the change happened (changed_at), and the type of change (change_type).
-- 	2.	Trigger Function (log_milk_report_changes):
-- 	•	This function is triggered on UPDATE or DELETE operations on the milk_report table.
-- 	•	It logs the old data into the milk_report_audit table before the update or delete occurs.
-- 	3.	Triggers (milk_report_update_audit, milk_report_delete_audit):
-- 	•	These triggers ensure that the log_milk_report_changes function is called whenever an UPDATE or DELETE operation happens on the milk_report table.

-- Summary:

-- With this setup, every change to the milk_report table (either update or delete) will be logged in the milk_report_audit table. This allows you to track the history of changes and see what the values were before the change was made.

CREATE TABLE milk_report_audit (
    audit_id bigint PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    milk_report_id bigint NOT NULL,
    changed_at timestamptz NOT NULL DEFAULT NOW(),
    change_type text NOT NULL, -- 'UPDATE' or 'DELETE'
    old_created_at timestamptz,
    old_updated_at timestamptz,
    old_deleted_at timestamptz,
    old_date date,
    old_agent text,
    old_user_name text,
    old_total_quantity_erp numeric,
    old_total_quantity_agent numeric,
    old_variance numeric,
    old_pdf_path text,
    old_erp_company_name text,
    old_id_cooperative bigint,
    old_id_user_cooperative bigint,
    FOREIGN KEY (milk_report_id) REFERENCES milk_report(id)
);


CREATE OR REPLACE FUNCTION log_milk_report_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO milk_report_audit (
            milk_report_id, 
            changed_at, 
            change_type,
            old_created_at, 
            old_updated_at, 
            old_deleted_at, 
            old_date, 
            old_agent, 
            old_user_name, 
            old_total_quantity_erp, 
            old_total_quantity_agent, 
            old_variance, 
            old_pdf_path, 
            old_erp_company_name, 
            old_id_cooperative,
            old_id_user_cooperative
        )
        VALUES (
            OLD.id, 
            NOW(), 
            'UPDATE',
            OLD.created_at, 
            OLD.updated_at, 
            OLD.deleted_at, 
            OLD.date, 
            OLD.agent, 
            OLD.user_name, 
            OLD.total_quantity_erp, 
            OLD.total_quantity_agent, 
            OLD.variance, 
            OLD.pdf_path, 
            OLD.erp_company_name, 
            OLD.id_cooperative,
            OLD.id_user_cooperative
        );
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO milk_report_audit (
            milk_report_id, 
            changed_at, 
            change_type,
            old_created_at, 
            old_updated_at, 
            old_deleted_at, 
            old_date, 
            old_agent, 
            old_user_name, 
            old_total_quantity_erp, 
            old_total_quantity_agent, 
            old_variance, 
            old_pdf_path, 
            old_erp_company_name, 
            old_id_cooperative,
            old_id_user_cooperative
        )
        VALUES (
            OLD.id, 
            NOW(), 
            'DELETE',
            OLD.created_at, 
            OLD.updated_at, 
            OLD.deleted_at, 
            OLD.date, 
            OLD.agent, 
            OLD.user_name, 
            OLD.total_quantity_erp, 
            OLD.total_quantity_agent, 
            OLD.variance, 
            OLD.pdf_path, 
            OLD.erp_company_name, 
            OLD.id_cooperative,
            OLD.id_user_cooperative
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;



CREATE TRIGGER milk_report_update_audit
AFTER UPDATE ON milk_report
FOR EACH ROW
EXECUTE FUNCTION log_milk_report_changes();

CREATE TRIGGER milk_report_delete_audit
AFTER DELETE ON milk_report
FOR EACH ROW
EXECUTE FUNCTION log_milk_report_changes();