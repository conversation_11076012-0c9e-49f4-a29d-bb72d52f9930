package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/templates/pages"
	"github.com/itunza/africascongress/templates/rates"
	"github.com/jackc/pgx/v5/pgtype"
)

type RatesHandler struct {
	db *database.Queries
}

func NewRatesHandler(db *database.Queries) *RatesHandler {
	return &RatesHandler{db: db}
}

// RatesPage renders the main rates page
func (h *RatesHandler) RatesPage(w http.ResponseWriter, r *http.Request) {
	cooperatives, err := h.db.ListCooperatives(r.Context())
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	component := pages.Rates(cooperatives)
	component.Render(r.Context(), w)
}

// ListRates returns rates for a specific cooperative
func (h *RatesHandler) ListRates(w http.ResponseWriter, r *http.Request) {
	cooperativeIDStr := r.URL.Query().Get("cooperative_id")
	if cooperativeIDStr == "" {
		http.Error(w, "cooperative_id is required", http.StatusBadRequest)
		return
	}

	cooperativeID, err := strconv.ParseInt(cooperativeIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid cooperative_id", http.StatusBadRequest)
		return
	}

	cooperativeID32 := int32(cooperativeID)
	ratesList, err := h.db.ListRatesByCooperative(r.Context(), &cooperativeID32)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	component := rates.List(ratesList)
	component.Render(r.Context(), w)
}

// GetAgentsForCooperative returns agents for a specific cooperative
func (h *RatesHandler) GetAgentsForCooperative(w http.ResponseWriter, r *http.Request) {
	cooperativeIDStr := r.URL.Query().Get("cooperative_id")
	if cooperativeIDStr == "" {
		http.Error(w, "cooperative_id is required", http.StatusBadRequest)
		return
	}

	cooperativeID, err := strconv.ParseInt(cooperativeIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid cooperative_id", http.StatusBadRequest)
		return
	}

	log.Printf("GetAgentsForCooperative: loading agents for cooperative %d", cooperativeID)
	agents, err := h.db.ListAllAgentsForCooperative(r.Context(), cooperativeID)
	if err != nil {
		log.Printf("GetAgentsForCooperative: error loading agents: %v", err)
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	log.Printf("GetAgentsForCooperative: found %d agents", len(agents))
	for _, agent := range agents {
		log.Printf("GetAgentsForCooperative: user_cooperative ID %d, email %s", agent.ID, agent.Email)
	}

	component := rates.AgentSelect(agents)
	component.Render(r.Context(), w)
}

// CreateRate creates a new rate entry
func (h *RatesHandler) CreateRate(w http.ResponseWriter, r *http.Request) {
	log.Printf("CreateRate called with method: %s", r.Method)

	if err := r.ParseForm(); err != nil {
		log.Printf("CreateRate: error parsing form: %v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Parse form values
	monthStr := r.FormValue("month")
	baseRateStr := r.FormValue("base_rate")
	deliveryMethod := r.FormValue("delivery_method")
	agentIDStr := r.FormValue("agent_id")
	cooperativeIDStr := r.FormValue("cooperative_id")
	deductionStr := r.FormValue("deduction")

	log.Printf("CreateRate: form values - month: %s, base_rate: %s, delivery_method: %s, agent_id: %s, cooperative_id: %s, deduction: %s",
		monthStr, baseRateStr, deliveryMethod, agentIDStr, cooperativeIDStr, deductionStr)

	// Validate required fields
	if monthStr == "" || baseRateStr == "" || cooperativeIDStr == "" {
		log.Printf("CreateRate: validation failed - month: %s, base_rate: %s, cooperative_id: %s", monthStr, baseRateStr, cooperativeIDStr)
		http.Error(w, "month, base_rate, and cooperative_id are required", http.StatusBadRequest)
		return
	}

	// Parse month
	month, err := time.Parse("2006-01-02", monthStr)
	if err != nil {
		http.Error(w, "Invalid month format", http.StatusBadRequest)
		return
	}

	// Parse base rate
	baseRate, err := strconv.ParseFloat(baseRateStr, 64)
	if err != nil {
		http.Error(w, "Invalid base_rate", http.StatusBadRequest)
		return
	}

	// Parse cooperative ID
	cooperativeID, err := strconv.ParseInt(cooperativeIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid cooperative_id", http.StatusBadRequest)
		return
	}

	// Parse optional agent ID
	var agentID *int32
	if agentIDStr != "" && agentIDStr != "0" {
		agentIDInt, err := strconv.ParseInt(agentIDStr, 10, 32)
		if err != nil {
			log.Printf("CreateRate: error parsing agent_id %s: %v", agentIDStr, err)
			http.Error(w, "Invalid agent_id", http.StatusBadRequest)
			return
		}
		agentID32 := int32(agentIDInt)
		agentID = &agentID32
		log.Printf("CreateRate: parsed agent_id: %d", agentID32)
	} else {
		log.Printf("CreateRate: no agent_id provided")
	}

	// Parse optional deduction
	var deduction float64
	if deductionStr != "" {
		deduction, err = strconv.ParseFloat(deductionStr, 64)
		if err != nil {
			http.Error(w, "Invalid deduction", http.StatusBadRequest)
			return
		}
	}

	// Create rate parameters (final_rate is calculated automatically by the database)
	params := database.CreateRateParams{
		Month:    pgtype.Date{Time: month, Valid: true},
		BaseRate: pgtype.Numeric{Int: nil, Exp: 0, NaN: false, Valid: true},
		DeliveryMethod: func() *string {
			if deliveryMethod == "" {
				return nil
			}
			return &deliveryMethod
		}(),
		AgentID: agentID,
		CooperativeID: func() *int32 {
			id := int32(cooperativeID)
			return &id
		}(),
		Deduction: pgtype.Numeric{Int: nil, Exp: 0, NaN: false, Valid: true},
	}

	// Set numeric values properly using string conversion
	err = params.BaseRate.Scan(fmt.Sprintf("%.2f", baseRate))
	if err != nil {
		http.Error(w, "Error processing base_rate", http.StatusInternalServerError)
		return
	}
	err = params.Deduction.Scan(fmt.Sprintf("%.2f", deduction))
	if err != nil {
		http.Error(w, "Error processing deduction", http.StatusInternalServerError)
		return
	}

	// Create the rate
	log.Printf("CreateRate: attempting to create rate with params: %+v", params)
	rateID, err := h.db.CreateRate(r.Context(), params)
	if err != nil {
		log.Printf("Error creating rate: %v", err)
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	log.Printf("CreateRate: successfully created rate with ID %d", rateID)

	// Return success response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"id":      rateID,
		"message": "Rate created successfully",
	})
}

// EditRate renders the edit form for a rate
func (h *RatesHandler) EditRate(w http.ResponseWriter, r *http.Request) {
	rateIDStr := r.URL.Query().Get("id")
	if rateIDStr == "" {
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	rateID, err := strconv.ParseInt(rateIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid id", http.StatusBadRequest)
		return
	}

	// Get the rate details
	rate, err := h.db.GetRate(r.Context(), int32(rateID))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Get agents for the cooperative
	var agents []database.ListAllAgentsForCooperativeRow
	if rate.CooperativeID != nil {
		agents, err = h.db.ListAllAgentsForCooperative(r.Context(), int64(*rate.CooperativeID))
		if err != nil {
			log.Printf("Error getting agents: %v", err)
			// Continue without agents if there's an error
		}
	}

	component := rates.EditForm(rate, agents)
	component.Render(r.Context(), w)
}

// UpdateRate updates an existing rate
func (h *RatesHandler) UpdateRate(w http.ResponseWriter, r *http.Request) {
	log.Printf("UpdateRate called with method: %s", r.Method)

	rateIDStr := r.URL.Query().Get("id")
	if rateIDStr == "" {
		log.Printf("UpdateRate: missing id parameter")
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	log.Printf("UpdateRate: processing rate ID %s", rateIDStr)

	rateID, err := strconv.ParseInt(rateIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid id", http.StatusBadRequest)
		return
	}

	if err := r.ParseForm(); err != nil {
		log.Printf("UpdateRate: error parsing form: %v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Parse form values (similar to CreateRate)
	monthStr := r.FormValue("month")
	baseRateStr := r.FormValue("base_rate")
	deliveryMethod := r.FormValue("delivery_method")
	agentIDStr := r.FormValue("agent_id")
	deductionStr := r.FormValue("deduction")

	log.Printf("UpdateRate: form values - month: %s, base_rate: %s, delivery_method: %s, agent_id: %s, deduction: %s",
		monthStr, baseRateStr, deliveryMethod, agentIDStr, deductionStr)

	// Validate required fields
	if monthStr == "" || baseRateStr == "" {
		log.Printf("UpdateRate: validation failed - month: %s, base_rate: %s", monthStr, baseRateStr)
		http.Error(w, "month and base_rate are required", http.StatusBadRequest)
		return
	}

	// Parse month
	month, err := time.Parse("2006-01-02", monthStr)
	if err != nil {
		log.Printf("UpdateRate: error parsing month %s: %v", monthStr, err)
		http.Error(w, "Invalid month format", http.StatusBadRequest)
		return
	}
	log.Printf("UpdateRate: parsed month: %v", month)

	// Parse base rate
	baseRate, err := strconv.ParseFloat(baseRateStr, 64)
	if err != nil {
		log.Printf("UpdateRate: error parsing base_rate %s: %v", baseRateStr, err)
		http.Error(w, "Invalid base_rate", http.StatusBadRequest)
		return
	}
	log.Printf("UpdateRate: parsed base_rate: %f", baseRate)

	// Parse optional agent ID
	var agentID *int32
	if agentIDStr != "" && agentIDStr != "0" {
		agentIDInt, err := strconv.ParseInt(agentIDStr, 10, 32)
		if err != nil {
			log.Printf("UpdateRate: error parsing agent_id %s: %v", agentIDStr, err)
			http.Error(w, "Invalid agent_id", http.StatusBadRequest)
			return
		}
		agentID32 := int32(agentIDInt)
		agentID = &agentID32
		log.Printf("UpdateRate: parsed agent_id: %d", agentID32)
	} else {
		log.Printf("UpdateRate: no agent_id provided")
	}

	// Parse optional deduction
	var deduction float64
	if deductionStr != "" {
		deduction, err = strconv.ParseFloat(deductionStr, 64)
		if err != nil {
			log.Printf("UpdateRate: error parsing deduction %s: %v", deductionStr, err)
			http.Error(w, "Invalid deduction", http.StatusBadRequest)
			return
		}
		log.Printf("UpdateRate: parsed deduction: %f", deduction)
	} else {
		log.Printf("UpdateRate: no deduction provided")
	}

	// Get the current rate to get cooperative_id
	currentRate, err := h.db.GetRate(r.Context(), int32(rateID))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Create update parameters (final_rate is calculated automatically by the database)
	params := database.UpdateRateParams{
		ID:       int32(rateID),
		Month:    pgtype.Date{Time: month, Valid: true},
		BaseRate: pgtype.Numeric{Int: nil, Exp: 0, NaN: false, Valid: true},
		DeliveryMethod: func() *string {
			if deliveryMethod == "" {
				return nil
			}
			return &deliveryMethod
		}(),
		AgentID:       agentID,
		CooperativeID: currentRate.CooperativeID,
		Deduction:     pgtype.Numeric{Int: nil, Exp: 0, NaN: false, Valid: true},
	}

	// Set numeric values properly using string conversion
	log.Printf("UpdateRate: setting BaseRate to %f", baseRate)
	err = params.BaseRate.Scan(fmt.Sprintf("%.2f", baseRate))
	if err != nil {
		log.Printf("UpdateRate: error scanning base_rate: %v", err)
		http.Error(w, "Error processing base_rate", http.StatusInternalServerError)
		return
	}

	log.Printf("UpdateRate: setting Deduction to %f", deduction)
	err = params.Deduction.Scan(fmt.Sprintf("%.2f", deduction))
	if err != nil {
		log.Printf("UpdateRate: error scanning deduction: %v", err)
		http.Error(w, "Error processing deduction", http.StatusInternalServerError)
		return
	}

	log.Printf("UpdateRate: all numeric values set successfully (final_rate will be calculated by database)")

	// Update the rate
	log.Printf("UpdateRate: attempting to update rate with params: %+v", params)
	err = h.db.UpdateRate(r.Context(), params)
	if err != nil {
		log.Printf("Error updating rate: %v", err)
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	log.Printf("UpdateRate: successfully updated rate %d", rateID)

	// Return the updated rate row
	updatedRate, err := h.db.GetRate(r.Context(), int32(rateID))
	if err != nil {
		log.Printf("UpdateRate: error getting updated rate: %v", err)
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	log.Printf("UpdateRate: retrieved updated rate: %+v", updatedRate)

	// Render the updated row
	component := rates.SingleRow(updatedRate)
	component.Render(r.Context(), w)
}

// CancelEdit returns the rate row to its normal display state
func (h *RatesHandler) CancelEdit(w http.ResponseWriter, r *http.Request) {
	rateIDStr := r.URL.Query().Get("id")
	if rateIDStr == "" {
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	rateID, err := strconv.ParseInt(rateIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid id", http.StatusBadRequest)
		return
	}

	// Get the rate details
	rate, err := h.db.GetRate(r.Context(), int32(rateID))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Render the normal row
	component := rates.SingleRow(rate)
	component.Render(r.Context(), w)
}

// DeleteRate deletes a rate entry
func (h *RatesHandler) DeleteRate(w http.ResponseWriter, r *http.Request) {
	rateIDStr := r.URL.Query().Get("id")
	if rateIDStr == "" {
		http.Error(w, "id is required", http.StatusBadRequest)
		return
	}

	rateID, err := strconv.ParseInt(rateIDStr, 10, 32)
	if err != nil {
		http.Error(w, "Invalid id", http.StatusBadRequest)
		return
	}

	err = h.db.DeleteRate(r.Context(), int32(rateID))
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Rate deleted successfully",
	})
}
