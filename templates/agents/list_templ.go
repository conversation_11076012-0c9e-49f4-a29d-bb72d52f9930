// Code generated by templ - DO NOT EDIT.

// templ: version: v0.2.778
package agents

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

func AgentList() templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<div id=\"agent-list\"><div class=\"mb-4\"><input type=\"text\" name=\"search\" placeholder=\"Search agents...\" class=\"w-full p-2 border border-gray-300 rounded-md\" hx-get=\"/api/cooperatives/list-agents\" hx-target=\"#agent-list\" hx-trigger=\"keyup changed delay:500ms\" hx-include=\"[name=&#39;search&#39;]\"></div><table class=\"min-w-full bg-white\"><thead><tr><th class=\"px-6 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Email</th><th class=\"px-6 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Phone Number</th></tr></thead> <tbody id=\"agent-table-body\"><!-- Table rows will be inserted here by JavaScript --></tbody></table></div><script>\n        htmx.on('#agent-list', 'htmx:afterSwap', function(evt) {\n            const agentList = evt.detail.elt;\n            const tableBody = agentList.querySelector('#agent-table-body');\n            const agents = JSON.parse(agentList.textContent);\n            \n            let tableHTML = '';\n            agents.forEach(agent => {\n                tableHTML += `\n                    <tr>\n                        <td class=\"px-6 py-4 whitespace-no-wrap border-b border-gray-500\">${agent.email}</td>\n                        <td class=\"px-6 py-4 whitespace-no-wrap border-b border-gray-500\">${agent.mobile_number}</td>\n                    </tr>\n                `;\n            });\n\n            tableBody.innerHTML = tableHTML;\n        });\n    </script>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return templ_7745c5c3_Err
	})
}

var _ = templruntime.GeneratedTemplate
