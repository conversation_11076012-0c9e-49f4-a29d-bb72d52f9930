// templates/farmers/add_modal.templ
package farmers

import (
    "strconv"
)

templ AddFarmerModal(cooperativeID int64, cooperativeName string) {
    <div id="add-farmer-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Modal Header -->
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Add New Farmer</h3>
                    <button onclick="closeAddFarmerModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- Cooperative Info -->
                <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                    <p class="text-sm text-blue-800">Adding farmer to: <strong>{cooperativeName}</strong></p>
                </div>

                <!-- Form -->
                <form hx-post="/api/cooperatives/add-farmer" 
                      hx-target="#add-farmer-modal" 
                      hx-swap="outerHTML"
                      class="space-y-4">
                    
                    <input type="hidden" name="id_cooperative" value={strconv.FormatInt(cooperativeID, 10)} />
                    
                    <div>
                        <label for="farmer-name" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                        <input type="text" id="farmer-name" name="name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter farmer's full name" />
                    </div>
                    
                    <div>
                        <label for="farmer-sno" class="block text-sm font-medium text-gray-700 mb-1">SNO *</label>
                        <input type="text" id="farmer-sno" name="sno" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter SNO" />
                    </div>
                    
                    <div>
                        <label for="farmer-phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                        <input type="tel" id="farmer-phone" name="mobile_number" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter phone number" />
                    </div>
                    
                    <div>
                        <label for="farmer-location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                        <input type="text" id="farmer-location" name="location"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter location (optional)" />
                    </div>
                    
                    <div>
                        <label for="farmer-gender" class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                        <select id="farmer-gender" name="gender"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="farmer-bank" class="block text-sm font-medium text-gray-700 mb-1">Bank</label>
                        <input type="text" id="farmer-bank" name="bank"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter bank name (optional)" />
                    </div>
                    
                    <div>
                        <label for="farmer-account" class="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                        <input type="text" id="farmer-account" name="account_number"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter account number (optional)" />
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeAddFarmerModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            Add Farmer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
}

templ AddFarmerSuccess(farmerName string) {
    <div id="add-farmer-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Farmer Added Successfully!</h3>
                <p class="text-sm text-gray-500 mb-4">{farmerName} has been added to the cooperative.</p>
                <button onclick="closeAddFarmerModal(); refreshFarmers();"
                        class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                    Close
                </button>
            </div>
        </div>
    </div>
}
