// templates/farmers/list.templ
package farmers

import (
    "github.com/itunza/africascongress/internal/database"
    "strconv"
)

templ FarmerList(farmers []database.Supplier) {
    <table class="min-w-full bg-white">
        <thead>
            <tr>
                <th class="px-6 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SNO</th>
                <th class="px-6 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Phone Number</th>
                <th class="px-6 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            for _, farmer := range farmers {
                <tr>
                    <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-500">{farmer.Name}</td>
                    <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-500">{farmer.Sno}</td>
                    <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-500">{farmer.MobileNumber}</td>
                    <td class="px-6 py-4 whitespace-no-wrap border-b border-gray-500">
                        <button hx-get={"/api/cooperatives/edit-farmer?id=" + strconv.Itoa(int(farmer.ID))}
                                hx-target="#farmer-form"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded">
                            Edit
                        </button>
                        <button hx-delete={"/api/cooperatives/delete-farmer?id=" + strconv.Itoa(int(farmer.ID))}
                                hx-confirm="Are you sure you want to delete this farmer?"
                                hx-target="closest tr"
                                hx-swap="outerHTML swap:1s"
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded ml-2">
                            Delete
                        </button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

