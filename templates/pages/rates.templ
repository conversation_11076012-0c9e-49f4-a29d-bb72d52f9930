package pages

import (
	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/templates/layout"
	"fmt"
)

templ Rates(cooperatives []database.Cooperative) {
	@layout.Base("Rates Management") {
		<div class="container mx-auto px-4 py-8">
			<div class="mb-8">
				<h1 class="text-3xl font-bold text-gray-900">Rates Management</h1>
				<p class="mt-2 text-gray-600">Manage rates for cooperatives and agents</p>
			</div>

			<!-- Cooperative Selection -->
			<div class="bg-white shadow rounded-lg p-6 mb-6">
				<h2 class="text-lg font-medium text-gray-900 mb-4">Select Cooperative</h2>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label for="cooperative_select" class="block text-sm font-medium text-gray-700">Cooperative</label>
						<select 
							id="cooperative_select" 
							name="cooperative_id"
							class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
							hx-get="/api/rates/list"
							hx-target="#rates-content"
							hx-trigger="change"
							hx-include="[name='cooperative_id']"
						>
							<option value="">Select a cooperative</option>
							for _, coop := range cooperatives {
								<option value={ fmt.Sprintf("%d", coop.ID) }>{ coop.Name }</option>
							}
						</select>
					</div>
				</div>
			</div>

			<!-- Add Rate Form -->
			<div id="rate-form-container" class="bg-white shadow rounded-lg p-6 mb-6" style="display: none;">
				<h2 class="text-lg font-medium text-gray-900 mb-4">Add New Rate</h2>
				<form
					id="rate-form"
					hx-post="/api/rates/create"
					hx-target="#rates-content"
					hx-on::after-request="if(event.detail.successful) { document.getElementById('rate-form').reset(); document.getElementById('cooperative_select').dispatchEvent(new Event('change')); }"
				>
					<input type="hidden" name="cooperative_id" id="form_cooperative_id">
					
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						<div>
							<label for="month" class="block text-sm font-medium text-gray-700">Month</label>
							<input
								type="date"
								name="month"
								id="month"
								required
								class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
							>
						</div>

						<div>
							<label for="base_rate" class="block text-sm font-medium text-gray-700">Base Rate</label>
							<input 
								type="number" 
								step="0.01" 
								name="base_rate" 
								id="base_rate" 
								required
								class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
								placeholder="0.00"
							>
						</div>

						<div>
							<label for="delivery_method" class="block text-sm font-medium text-gray-700">Delivery Method</label>
							<select
								name="delivery_method"
								id="delivery_method"
								class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
							>
								<option value="">Select delivery method</option>
								<option value="agent">Agent</option>
								<option value="chiller">Chiller</option>
							</select>
						</div>

						<div>
							<label for="agent_id" class="block text-sm font-medium text-gray-700">Agent</label>
							<div id="agent-select-container">
								<select name="agent_id" id="agent_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
									<option value="">Select cooperative first</option>
								</select>
							</div>
						</div>

						<div>
							<label for="deduction" class="block text-sm font-medium text-gray-700">Deduction</label>
							<input 
								type="number" 
								step="0.01" 
								name="deduction" 
								id="deduction" 
								value="0"
								class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
								placeholder="0.00"
							>
						</div>
					</div>

					<div class="mt-6">
						<button 
							type="submit"
							class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
						>
							Add Rate
						</button>
					</div>
				</form>
			</div>

			<!-- Rates List -->
			<div class="bg-white shadow rounded-lg p-6">
				<h2 class="text-lg font-medium text-gray-900 mb-4">Rates List</h2>
				<div id="rates-content">
					<div class="text-center py-8 text-gray-500">
						<p>Select a cooperative to view rates</p>
					</div>
				</div>
			</div>
		</div>

		<script>
			// Show/hide rate form when cooperative is selected
			document.getElementById('cooperative_select').addEventListener('change', function() {
				const formContainer = document.getElementById('rate-form-container');
				const formCoopId = document.getElementById('form_cooperative_id');
				const agentContainer = document.getElementById('agent-select-container');

				if (this.value) {
					formContainer.style.display = 'block';
					formCoopId.value = this.value;

					// Load agents for the selected cooperative
					fetch(`/api/rates/agents?cooperative_id=${this.value}`)
						.then(response => response.text())
						.then(html => {
							agentContainer.innerHTML = html;
						})
						.catch(error => {
							console.error('Error loading agents:', error);
							agentContainer.innerHTML = '<select name="agent_id" id="agent_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"><option value="">Error loading agents</option></select>';
						});
				} else {
					formContainer.style.display = 'none';
					formCoopId.value = '';
					agentContainer.innerHTML = '<select name="agent_id" id="agent_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"><option value="">Select cooperative first</option></select>';
				}
			});
		</script>
	}
}
