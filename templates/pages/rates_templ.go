// Code generated by templ - DO NOT EDIT.

// templ: version: v0.2.778
package pages

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"fmt"
	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/templates/layout"
)

func Rates(cooperatives []database.Cooperative) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Var2 := templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
			templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
			templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
			if !templ_7745c5c3_IsBuffer {
				defer func() {
					templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err == nil {
						templ_7745c5c3_Err = templ_7745c5c3_BufErr
					}
				}()
			}
			ctx = templ.InitializeContext(ctx)
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<div class=\"container mx-auto px-4 py-8\"><div class=\"mb-8\"><h1 class=\"text-3xl font-bold text-gray-900\">Rates Management</h1><p class=\"mt-2 text-gray-600\">Manage rates for cooperatives and agents</p></div><!-- Cooperative Selection --><div class=\"bg-white shadow rounded-lg p-6 mb-6\"><h2 class=\"text-lg font-medium text-gray-900 mb-4\">Select Cooperative</h2><div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\"><div><label for=\"cooperative_select\" class=\"block text-sm font-medium text-gray-700\">Cooperative</label> <select id=\"cooperative_select\" name=\"cooperative_id\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm\" hx-get=\"/api/rates/list\" hx-target=\"#rates-content\" hx-trigger=\"change\" hx-include=\"[name=&#39;cooperative_id&#39;]\"><option value=\"\">Select a cooperative</option> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			for _, coop := range cooperatives {
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<option value=\"")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var3 string
				templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(fmt.Sprintf("%d", coop.ID))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `templates/pages/rates.templ`, Line: 34, Col: 50}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var4 string
				templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(coop.Name)
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `templates/pages/rates.templ`, Line: 34, Col: 64}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("</option>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("</select></div></div></div><!-- Add Rate Form --><div id=\"rate-form-container\" class=\"bg-white shadow rounded-lg p-6 mb-6\" style=\"display: none;\"><h2 class=\"text-lg font-medium text-gray-900 mb-4\">Add New Rate</h2><form id=\"rate-form\" hx-post=\"/api/rates/create\" hx-target=\"#rates-content\" hx-on::after-request=\"if(event.detail.successful) { document.getElementById(&#39;rate-form&#39;).reset(); document.getElementById(&#39;cooperative_select&#39;).dispatchEvent(new Event(&#39;change&#39;)); }\"><input type=\"hidden\" name=\"cooperative_id\" id=\"form_cooperative_id\"><div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\"><div><label for=\"month\" class=\"block text-sm font-medium text-gray-700\">Month</label> <input type=\"date\" name=\"month\" id=\"month\" required class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm\"></div><div><label for=\"base_rate\" class=\"block text-sm font-medium text-gray-700\">Base Rate</label> <input type=\"number\" step=\"0.01\" name=\"base_rate\" id=\"base_rate\" required class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm\" placeholder=\"0.00\"></div><div><label for=\"delivery_method\" class=\"block text-sm font-medium text-gray-700\">Delivery Method</label> <select name=\"delivery_method\" id=\"delivery_method\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm\"><option value=\"\">Select delivery method</option> <option value=\"agent\">Agent</option> <option value=\"chiller\">Chiller</option></select></div><div><label for=\"agent_id\" class=\"block text-sm font-medium text-gray-700\">Agent</label><div id=\"agent-select-container\"><select name=\"agent_id\" id=\"agent_id\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm\"><option value=\"\">Select cooperative first</option></select></div></div><div><label for=\"deduction\" class=\"block text-sm font-medium text-gray-700\">Deduction</label> <input type=\"number\" step=\"0.01\" name=\"deduction\" id=\"deduction\" value=\"0\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm\" placeholder=\"0.00\"></div></div><div class=\"mt-6\"><button type=\"submit\" class=\"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\">Add Rate</button></div></form></div><!-- Rates List --><div class=\"bg-white shadow rounded-lg p-6\"><h2 class=\"text-lg font-medium text-gray-900 mb-4\">Rates List</h2><div id=\"rates-content\"><div class=\"text-center py-8 text-gray-500\"><p>Select a cooperative to view rates</p></div></div></div></div><script>\n\t\t\t// Show/hide rate form when cooperative is selected\n\t\t\tdocument.getElementById('cooperative_select').addEventListener('change', function() {\n\t\t\t\tconst formContainer = document.getElementById('rate-form-container');\n\t\t\t\tconst formCoopId = document.getElementById('form_cooperative_id');\n\t\t\t\tconst agentContainer = document.getElementById('agent-select-container');\n\n\t\t\t\tif (this.value) {\n\t\t\t\t\tformContainer.style.display = 'block';\n\t\t\t\t\tformCoopId.value = this.value;\n\n\t\t\t\t\t// Load agents for the selected cooperative\n\t\t\t\t\tfetch(`/api/rates/agents?cooperative_id=${this.value}`)\n\t\t\t\t\t\t.then(response => response.text())\n\t\t\t\t\t\t.then(html => {\n\t\t\t\t\t\t\tagentContainer.innerHTML = html;\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\t\tconsole.error('Error loading agents:', error);\n\t\t\t\t\t\t\tagentContainer.innerHTML = '<select name=\"agent_id\" id=\"agent_id\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm\"><option value=\"\">Error loading agents</option></select>';\n\t\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tformContainer.style.display = 'none';\n\t\t\t\t\tformCoopId.value = '';\n\t\t\t\t\tagentContainer.innerHTML = '<select name=\"agent_id\" id=\"agent_id\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm\"><option value=\"\">Select cooperative first</option></select>';\n\t\t\t\t}\n\t\t\t});\n\t\t</script>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			return templ_7745c5c3_Err
		})
		templ_7745c5c3_Err = layout.Base("Rates Management").Render(templ.WithChildren(ctx, templ_7745c5c3_Var2), templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return templ_7745c5c3_Err
	})
}

var _ = templruntime.GeneratedTemplate
