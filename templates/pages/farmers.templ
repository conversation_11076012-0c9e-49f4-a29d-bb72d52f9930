// templates/pages/farmers.templ
package pages

import (
    "github.com/itunza/africascongress/templates/layout"
     "strconv"
    "github.com/itunza/africascongress/internal/database"
)

templ Farmers(cooperatives []database.Cooperative) {
    @layout.Base("Farmers") {
        <div class="container mx-auto px-4 py-8">
            <!-- Header Section -->
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800">Farmers Management</h1>
                <div class="flex space-x-3">
                    <button id="refresh-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Refresh</span>
                    </button>
                    <button id="export-btn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Export</span>
                    </button>
                </div>
            </div>

            <!-- Cooperative Selection & Search Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Cooperative Selection -->
                    <div>
                        <label for="cooperative-select" class="block text-sm font-medium text-gray-700 mb-2">Select Cooperative</label>
                        <select id="cooperative-select" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                hx-get="/api/cooperatives/list-farmers"
                                hx-target="#farmer-list"
                                hx-trigger="change"
                                hx-include="[name='cooperative_id']">
                            <option value="">Select a cooperative</option>
                            for _, coop := range cooperatives {
                                <option value={strconv.Itoa(int(coop.ID))}>{coop.Name}</option>
                            }
                        </select>
                        <input type="hidden" name="cooperative_id" value="">
                    </div>

                    <!-- Search Section -->
                    <div>
                        <label for="search-input" class="block text-sm font-medium text-gray-700 mb-2">Search Farmers</label>
                        <div class="relative">
                            <input type="text" id="search-input" placeholder="Search by name, SNO, or phone..."
                                   class="w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   hx-get="/api/cooperatives/search-farmers"
                                   hx-target="#farmer-list"
                                   hx-trigger="keyup changed delay:300ms"
                                   hx-include="[name='cooperative_id']">
                            <svg class="absolute left-3 top-3.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Row -->
                <div class="mt-6 flex flex-wrap gap-3">
                    <button id="add-farmer-btn" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span>Add Farmer</span>
                    </button>
                    <button id="bulk-import-btn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <span>Bulk Import</span>
                    </button>
                    <button id="sync-farmers-btn" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Sync with ERP</span>
                    </button>
                </div>
            </div>

            <!-- Farmer List Section -->
            <div id="farmer-list" class="bg-white rounded-lg shadow-md">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                        <div id="farmer-stats" class="text-sm text-gray-500"></div>
                    </div>
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No farmers to display</h3>
                        <p class="mt-1 text-sm text-gray-500">Please select a cooperative to view farmers.</p>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let currentCooperativeId = '';
            let allFarmers = [];

            // Initialize page functionality
            document.addEventListener('DOMContentLoaded', function() {
                initializeEventListeners();
            });

            function initializeEventListeners() {
                // Cooperative selection handler
                document.getElementById('cooperative-select').addEventListener('change', function() {
                    currentCooperativeId = this.value;
                    document.querySelector('[name="cooperative_id"]').value = this.value;

                    // Enable/disable action buttons based on selection
                    toggleActionButtons(!!this.value);

                    // Clear search when changing cooperative
                    document.getElementById('search-input').value = '';
                });

                // Search input handler
                document.getElementById('search-input').addEventListener('input', function() {
                    const query = this.value.trim();
                    if (query.length === 0 && currentCooperativeId) {
                        // If search is cleared, reload all farmers for the cooperative
                        htmx.trigger('#cooperative-select', 'change');
                    }
                });

                // Action button handlers
                document.getElementById('refresh-btn').addEventListener('click', refreshFarmers);
                document.getElementById('export-btn').addEventListener('click', exportFarmers);
                document.getElementById('add-farmer-btn').addEventListener('click', showAddFarmerModal);
                document.getElementById('bulk-import-btn').addEventListener('click', showBulkImportModal);
                document.getElementById('sync-farmers-btn').addEventListener('click', syncFarmers);
            }

            function toggleActionButtons(enabled) {
                const buttons = ['add-farmer-btn', 'bulk-import-btn', 'sync-farmers-btn'];
                buttons.forEach(btnId => {
                    const btn = document.getElementById(btnId);
                    btn.disabled = !enabled;
                    if (enabled) {
                        btn.classList.remove('opacity-50', 'cursor-not-allowed');
                    } else {
                        btn.classList.add('opacity-50', 'cursor-not-allowed');
                    }
                });
            }

            function refreshFarmers() {
                if (currentCooperativeId) {
                    htmx.trigger('#cooperative-select', 'change');
                    showNotification('Farmers list refreshed', 'success');
                }
            }

            function exportFarmers() {
                if (!currentCooperativeId) {
                    showNotification('Please select a cooperative first', 'error');
                    return;
                }

                // Create CSV content
                const csvContent = generateCSV(allFarmers);
                downloadCSV(csvContent, `farmers_${currentCooperativeId}_${new Date().toISOString().split('T')[0]}.csv`);
                showNotification('Farmers data exported successfully', 'success');
            }

            function generateCSV(farmers) {
                const headers = ['Name', 'SNO', 'Phone Number', 'Location', 'Gender', 'Bank', 'Account Number'];
                const csvRows = [headers.join(',')];

                farmers.forEach(farmer => {
                    const row = [
                        `"${farmer.name || ''}"`,
                        `"${farmer.sno || ''}"`,
                        `"${farmer.mobile_number || ''}"`,
                        `"${farmer.location || ''}"`,
                        `"${farmer.gender || ''}"`,
                        `"${farmer.bank || ''}"`,
                        `"${farmer.account_number || ''}"`
                    ];
                    csvRows.push(row.join(','));
                });

                return csvRows.join('\n');
            }

            function downloadCSV(content, filename) {
                const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                if (link.download !== undefined) {
                    const url = URL.createObjectURL(blob);
                    link.setAttribute('href', url);
                    link.setAttribute('download', filename);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }

            function showAddFarmerModal() {
                if (!currentCooperativeId) {
                    showNotification('Please select a cooperative first', 'error');
                    return;
                }

                // Fetch the add farmer modal
                fetch(`/api/cooperatives/add-farmer-form?cooperative_id=${currentCooperativeId}`)
                    .then(response => response.text())
                    .then(html => {
                        document.body.insertAdjacentHTML('beforeend', html);
                    })
                    .catch(error => {
                        showNotification('Error loading add farmer form: ' + error.message, 'error');
                    });
            }

            // Global function to close the modal
            window.closeAddFarmerModal = function() {
                const modal = document.getElementById('add-farmer-modal');
                if (modal) {
                    modal.remove();
                }
            }

            function showBulkImportModal() {
                // TODO: Implement bulk import modal
                showNotification('Bulk import functionality coming soon', 'info');
            }

            function syncFarmers() {
                if (!currentCooperativeId) {
                    showNotification('Please select a cooperative first', 'error');
                    return;
                }

                showNotification('Syncing farmers with ERP...', 'info');

                fetch(`/api/cooperatives/sync-farmers?cooperative_id=${currentCooperativeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    showNotification('Farmers synced successfully', 'success');
                    refreshFarmers();
                })
                .catch(error => {
                    showNotification('Error syncing farmers: ' + error.message, 'error');
                });
            }

            function showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${getNotificationClasses(type)}`;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Auto remove after 3 seconds
                setTimeout(() => {
                    notification.classList.add('opacity-0', 'translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            function getNotificationClasses(type) {
                switch (type) {
                    case 'success': return 'bg-green-500 text-white';
                    case 'error': return 'bg-red-500 text-white';
                    case 'warning': return 'bg-yellow-500 text-white';
                    default: return 'bg-blue-500 text-white';
                }
            }

            // HTMX event handlers
            htmx.on('#farmer-list', 'htmx:afterSwap', function(evt) {
                const farmerList = evt.detail.elt;
                let farmers = [];

                try {
                    farmers = JSON.parse(farmerList.textContent);
                    allFarmers = farmers; // Store for export functionality
                } catch (e) {
                    console.error('Error parsing farmer data:', e);
                    return;
                }

                updateFarmerStats(farmers.length);

                if (farmers.length === 0) {
                    renderEmptyState();
                } else {
                    renderFarmerTable(farmers);
                }
            });

            function updateFarmerStats(count) {
                const statsElement = document.getElementById('farmer-stats');
                if (statsElement) {
                    statsElement.textContent = `Total: ${count} farmer${count !== 1 ? 's' : ''}`;
                }
            }

            function renderEmptyState() {
                const farmerList = document.getElementById('farmer-list');
                farmerList.innerHTML = `
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                            <div id="farmer-stats" class="text-sm text-gray-500">Total: 0 farmers</div>
                        </div>
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No farmers found</h3>
                            <p class="mt-1 text-sm text-gray-500">No farmers found for the selected criteria.</p>
                        </div>
                    </div>
                `;
            }

            function renderFarmerTable(farmers) {
                const farmerList = document.getElementById('farmer-list');

                let tableHTML = `
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                            <div id="farmer-stats" class="text-sm text-gray-500">Total: ${farmers.length} farmer${farmers.length !== 1 ? 's' : ''}</div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SNO</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                `;

                farmers.forEach((farmer, index) => {
                    const rowClass = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
                    tableHTML += `
                        <tr class="${rowClass} hover:bg-gray-100 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${farmer.name || 'N/A'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">${farmer.sno || 'N/A'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">${farmer.mobile_number || 'N/A'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">${farmer.location || 'N/A'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGenderBadgeClass(farmer.gender)}">
                                    ${farmer.gender || 'N/A'}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">${farmer.bank || 'N/A'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="editFarmer(${farmer.id})" class="text-indigo-600 hover:text-indigo-900 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button onclick="deleteFarmer(${farmer.id})" class="text-red-600 hover:text-red-900 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });

                tableHTML += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;

                farmerList.innerHTML = tableHTML;
            }

            function getGenderBadgeClass(gender) {
                switch (gender?.toLowerCase()) {
                    case 'male': return 'bg-blue-100 text-blue-800';
                    case 'female': return 'bg-pink-100 text-pink-800';
                    default: return 'bg-gray-100 text-gray-800';
                }
            }

            function editFarmer(farmerId) {
                // TODO: Implement edit farmer functionality
                showNotification(`Edit farmer ${farmerId} - Coming soon`, 'info');
            }

            function deleteFarmer(farmerId) {
                if (confirm('Are you sure you want to delete this farmer?')) {
                    // TODO: Implement delete farmer functionality
                    showNotification(`Delete farmer ${farmerId} - Coming soon`, 'info');
                }
            }
        </script>
    }
}
