// templates/pages/farmers.templ
package pages

import (
    "github.com/itunza/africascongress/templates/layout"
     "strconv"
    "github.com/itunza/africascongress/internal/database"
)

templ Farmers(cooperatives []database.Cooperative) {
    @layout.Base("Farmers") {
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-4xl font-bold mb-8 text-gray-800">Farmers</h1>

            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4 text-gray-700">Select Cooperative</h2>
                <select id="cooperative-select" class="w-full p-2 border border-gray-300 rounded-md"
                        hx-get="/api/cooperatives/list-farmers"
                        hx-target="#farmer-list"
                        hx-trigger="change"
                        hx-include="[name='cooperative_id']">
                    <option value="">Select a cooperative</option>
                    for _, coop := range cooperatives {
                        <option value={strconv.Itoa(int(coop.ID))}>{coop.Name}</option>
                    }
                </select>
                <input type="hidden" name="cooperative_id" value="">
            </div>

            <div id="farmer-list" class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-2xl font-semibold mb-4 text-gray-700">Farmer List</h2>
                <p class="text-gray-600">Please select a cooperative to view farmers.</p>
            </div>
        </div>

        <script>
            document.getElementById('cooperative-select').addEventListener('change', function() {
                document.querySelector('[name="cooperative_id"]').value = this.value;
            });

            htmx.on('#farmer-list', 'htmx:afterSwap', function(evt) {
                const farmerList = evt.detail.elt;
                const farmers = JSON.parse(farmerList.textContent);
                
                if (farmers.length === 0) {
                    farmerList.innerHTML = '<p class="text-gray-600">No farmers found for this cooperative.</p>';
                } else {
                    let tableHTML = `
                        <table class="min-w-full divide-y divide-gray-200 mt-4">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SNO</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone Number</th>
                                 
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    farmers.forEach(farmer => {
                        tableHTML += `
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${farmer.name}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${farmer.sno}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${farmer.mobile_number}</td>
         
                            </tr>
                        `;
                    });

                    tableHTML += `
                            </tbody>
                        </table>
                    `;

                    farmerList.innerHTML = tableHTML;
                }
            });
        </script>
    }
}
