// templates/pages/farmers.templ
package pages

import (
    "github.com/itunza/africascongress/templates/layout"
     "strconv"
    "github.com/itunza/africascongress/internal/database"
)

templ Farmers(cooperatives []database.Cooperative) {
    @layout.Base("Farmers") {
        <div class="container mx-auto px-4 py-8">
            <!-- Header Section -->
            <div class="flex justify-between items-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800">Farmers Management</h1>
                <div class="flex space-x-3">
                    <button id="refresh-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Refresh</span>
                    </button>
                    <button id="export-btn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Export</span>
                    </button>
                </div>
            </div>

            <!-- Cooperative Selection & Search Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Cooperative Selection -->
                    <div>
                        <label for="cooperative-select" class="block text-sm font-medium text-gray-700 mb-2">Select Cooperative</label>
                        <select id="cooperative-select" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                hx-get="/api/cooperatives/list-farmers"
                                hx-target="#farmer-list"
                                hx-trigger="change"
                                hx-include="[name='cooperative_id']">
                            <option value="">Select a cooperative</option>
                            for _, coop := range cooperatives {
                                <option value={strconv.Itoa(int(coop.ID))}>{coop.Name}</option>
                            }
                        </select>
                        <input type="hidden" name="cooperative_id" value="">
                    </div>

                    <!-- Search Section -->
                    <div>
                        <label for="search-input" class="block text-sm font-medium text-gray-700 mb-2">Search Farmers</label>
                        <div class="relative">
                            <input type="text" id="search-input" name="q" placeholder="Search by name, SNO, or phone..."
                                   class="w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   hx-get="/api/cooperatives/search-farmers"
                                   hx-target="#farmer-list"
                                   hx-trigger="keyup changed delay:300ms"
                                   hx-include="[name='cooperative_id'], [name='q']"
                                   hx-indicator="#search-loading">
                            <svg class="absolute left-3 top-3.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <!-- Loading indicator -->
                            <div id="search-loading" class="absolute right-3 top-3.5 htmx-indicator">
                                <svg class="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons Row -->
                <div class="mt-6 flex flex-wrap gap-3">
                    <button id="add-farmer-btn" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span>Add Farmer</span>
                    </button>
                    <button id="bulk-import-btn" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <span>Bulk Import</span>
                    </button>
                    <button id="sync-farmers-btn" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>Sync with ERP</span>
                    </button>
                </div>
            </div>

            <!-- Farmer List Section -->
            <div id="farmer-list" class="bg-white rounded-lg shadow-md">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                        <div id="farmer-stats" class="text-sm text-gray-500"></div>
                    </div>
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No farmers to display</h3>
                        <p class="mt-1 text-sm text-gray-500">Please select a cooperative to view farmers.</p>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let currentCooperativeId = '';
            let allFarmers = [];

            // Initialize page functionality
            document.addEventListener('DOMContentLoaded', function() {
                initializeEventListeners();
            });

            function initializeEventListeners() {
                // Cooperative selection handler
                document.getElementById('cooperative-select').addEventListener('change', function() {
                    currentCooperativeId = this.value;
                    document.querySelector('[name="cooperative_id"]').value = this.value;

                    // Enable/disable action buttons based on selection
                    toggleActionButtons(!!this.value);

                    // Clear search when changing cooperative
                    document.getElementById('search-input').value = '';
                });

                // Action button handlers
                document.getElementById('refresh-btn').addEventListener('click', refreshFarmers);
                document.getElementById('export-btn').addEventListener('click', exportFarmers);
                document.getElementById('add-farmer-btn').addEventListener('click', showAddFarmerModal);
                document.getElementById('bulk-import-btn').addEventListener('click', showBulkImportModal);
                document.getElementById('sync-farmers-btn').addEventListener('click', syncFarmers);
            }

            function toggleActionButtons(enabled) {
                const buttons = ['add-farmer-btn', 'bulk-import-btn', 'sync-farmers-btn'];
                buttons.forEach(btnId => {
                    const btn = document.getElementById(btnId);
                    btn.disabled = !enabled;
                    if (enabled) {
                        btn.classList.remove('opacity-50', 'cursor-not-allowed');
                    } else {
                        btn.classList.add('opacity-50', 'cursor-not-allowed');
                    }
                });
            }

            function refreshFarmers() {
                if (currentCooperativeId) {
                    htmx.trigger('#cooperative-select', 'change');
                    showNotification('Farmers list refreshed', 'success');
                }
            }

            function exportFarmers() {
                if (!currentCooperativeId) {
                    showNotification('Please select a cooperative first', 'error');
                    return;
                }

                // Create CSV content
                const csvContent = generateCSV(allFarmers);
                downloadCSV(csvContent, `farmers_${currentCooperativeId}_${new Date().toISOString().split('T')[0]}.csv`);
                showNotification('Farmers data exported successfully', 'success');
            }

            function generateCSV(farmers) {
                const headers = ['Name', 'SNO', 'Phone Number', 'Location', 'Gender', 'Bank', 'Account Number'];
                const csvRows = [headers.join(',')];

                farmers.forEach(farmer => {
                    const row = [
                        `"${farmer.name || ''}"`,
                        `"${farmer.sno || ''}"`,
                        `"${farmer.mobile_number || ''}"`,
                        `"${farmer.location || ''}"`,
                        `"${farmer.gender || ''}"`,
                        `"${farmer.bank || ''}"`,
                        `"${farmer.account_number || ''}"`
                    ];
                    csvRows.push(row.join(','));
                });

                return csvRows.join('\n');
            }

            function downloadCSV(content, filename) {
                const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                if (link.download !== undefined) {
                    const url = URL.createObjectURL(blob);
                    link.setAttribute('href', url);
                    link.setAttribute('download', filename);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }

            function showAddFarmerModal() {
                showNotification('Add farmer functionality coming soon', 'info');
            }

            function showBulkImportModal() {
                showNotification('Bulk import functionality coming soon', 'info');
            }

            function syncFarmers() {
                if (!currentCooperativeId) {
                    showNotification('Please select a cooperative first', 'error');
                    return;
                }

                showNotification('Syncing farmers with ERP...', 'info');

                fetch(`/api/cooperatives/sync-farmers?cooperative_id=${currentCooperativeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    showNotification('Farmers synced successfully', 'success');
                    refreshFarmers();
                })
                .catch(error => {
                    showNotification('Error syncing farmers: ' + error.message, 'error');
                });
            }

            function showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${getNotificationClasses(type)}`;
                notification.textContent = message;

                document.body.appendChild(notification);

                // Auto remove after 3 seconds
                setTimeout(() => {
                    notification.classList.add('opacity-0', 'translate-x-full');
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            function getNotificationClasses(type) {
                switch (type) {
                    case 'success': return 'bg-green-500 text-white';
                    case 'error': return 'bg-red-500 text-white';
                    case 'warning': return 'bg-yellow-500 text-white';
                    default: return 'bg-blue-500 text-white';
                }
            }

            // HTMX event handlers
            htmx.on('#farmer-list', 'htmx:afterSwap', function(evt) {
                console.log('HTMX afterSwap triggered');
                const farmerList = evt.detail.elt;
                let farmers = [];

                try {
                    console.log('Raw response:', farmerList.textContent);
                    farmers = JSON.parse(farmerList.textContent);
                    allFarmers = farmers; // Store for export functionality
                    console.log('Parsed farmers:', farmers);
                } catch (e) {
                    console.error('Error parsing farmer data:', e);
                    console.log('Raw content that failed to parse:', farmerList.textContent);
                    return;
                }

                updateFarmerStats(farmers.length);

                if (farmers.length === 0) {
                    renderEmptyState();
                } else {
                    renderFarmerTable(farmers);
                }
            });

            // Add HTMX error handling
            htmx.on('htmx:responseError', function(evt) {
                console.error('HTMX Response Error:', evt.detail);
                showNotification('Error loading data: ' + evt.detail.xhr.status, 'error');
            });

            htmx.on('htmx:sendError', function(evt) {
                console.error('HTMX Send Error:', evt.detail);
                showNotification('Network error occurred', 'error');
            });

            function updateFarmerStats(count) {
                const statsElement = document.getElementById('farmer-stats');
                if (statsElement) {
                    statsElement.textContent = `Total: ${count} farmer${count !== 1 ? 's' : ''}`;
                }
            }

            function renderEmptyState() {
                const farmerList = document.getElementById('farmer-list');
                farmerList.innerHTML = `
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                            <div id="farmer-stats" class="text-sm text-gray-500">Total: 0 farmers</div>
                        </div>
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No farmers found</h3>
                            <p class="mt-1 text-sm text-gray-500">No farmers found for the selected criteria.</p>
                        </div>
                    </div>
                `;
            }

            function renderFarmerTable(farmers) {
                const farmerList = document.getElementById('farmer-list');

                let tableHTML = `
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-xl font-semibold text-gray-700">Farmer List</h2>
                            <div id="farmer-stats" class="text-sm text-gray-500">Total: ${farmers.length} farmer${farmers.length !== 1 ? 's' : ''}</div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SNO</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                `;

                farmers.forEach((farmer, index) => {
                    const rowClass = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
                    tableHTML += `
                        <tr class="${rowClass} hover:bg-gray-100 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">${farmer.name || 'N/A'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">${farmer.sno || 'N/A'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">${farmer.mobile_number || 'N/A'}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="editFarmer(${farmer.id})" class="text-indigo-600 hover:text-indigo-900 transition-colors">
                                        Edit
                                    </button>
                                    <button onclick="deleteFarmer(${farmer.id})" class="text-red-600 hover:text-red-900 transition-colors">
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });

                tableHTML += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;

                farmerList.innerHTML = tableHTML;
            }

            function editFarmer(farmerId) {
                showNotification(`Edit farmer ${farmerId} - Coming soon`, 'info');
            }

            function deleteFarmer(farmerId) {
                if (confirm('Are you sure you want to delete this farmer?')) {
                    showNotification(`Delete farmer ${farmerId} - Coming soon`, 'info');
                }
            }
        </script>
    }
}
