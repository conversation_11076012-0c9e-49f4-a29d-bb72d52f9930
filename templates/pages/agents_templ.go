// Code generated by templ - DO NOT EDIT.

// templ: version: v0.2.778
// templates/pages/agents.templ

package pages

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/templates/components"
	"github.com/itunza/africascongress/templates/layout"
)

func Agents(cooperatives []database.Cooperative) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Var2 := templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
			templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
			templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
			if !templ_7745c5c3_IsBuffer {
				defer func() {
					templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err == nil {
						templ_7745c5c3_Err = templ_7745c5c3_BufErr
					}
				}()
			}
			ctx = templ.InitializeContext(ctx)
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<div class=\"container mx-auto px-4 py-8\"><h1 class=\"text-4xl font-bold mb-8 text-gray-800\">Agents Management</h1><div class=\"bg-white rounded-lg shadow-md p-6 mb-8\"><h2 class=\"text-2xl font-semibold mb-4 text-gray-700\">Select Cooperative</h2>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = components.CooperativeSelect(cooperatives, "#agent-list", "/api/cooperatives/list-agents").Render(ctx, templ_7745c5c3_Buffer)
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<button hx-get=\"/api/cooperatives/add-agent-form\" hx-target=\"#agent-form\" hx-vals=\"js:{&#34;cooperative_id&#34;: document.getElementById(&#34;cooperative-select&#34;).value}\" class=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded mt-4 transition duration-300 ease-in-out\">Add New Agent</button></div><div id=\"agent-form\" class=\"mb-8\"></div><div id=\"agent-list\" class=\"bg-white rounded-lg shadow-md p-6\"><h2 class=\"text-2xl font-semibold mb-4 text-gray-700\">Agent List</h2><p class=\"text-gray-600\">Please select a cooperative to view agents.</p></div></div><script>\n            htmx.on('#agent-list', 'htmx:afterSwap', function(evt) {\n                const agentList = evt.detail.elt;\n                try {\n                    const agents = JSON.parse(agentList.textContent);\n                    \n                    let tableHTML = `\n                        <div class=\"overflow-x-auto\">\n                            <table class=\"min-w-full bg-white\">\n                                <thead class=\"bg-gray-100\">\n                                    <tr>\n                                        <th class=\"px-6 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Email</th>\n                                        <th class=\"px-6 py-3 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Phone Number</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                    `;\n\n                    agents.forEach(agent => {\n                        tableHTML += `\n                            <tr class=\"hover:bg-gray-50\">\n                                <td class=\"px-6 py-4 whitespace-no-wrap border-b border-gray-200\">${agent.email}</td>\n                                <td class=\"px-6 py-4 whitespace-no-wrap border-b border-gray-200\">${agent.mobile_number}</td>\n                            </tr>\n                        `;\n                    });\n\n                    tableHTML += `\n                                </tbody>\n                            </table>\n                        </div>\n                    `;\n\n                    agentList.innerHTML = tableHTML;\n                } catch (e) {\n                    console.error(\"Error parsing JSON:\", e);\n                    agentList.innerHTML = \"<p class='text-red-500'>Error loading agents. Please try again.</p>\";\n                }\n            });\n        </script>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			return templ_7745c5c3_Err
		})
		templ_7745c5c3_Err = layout.Base("Agents").Render(templ.WithChildren(ctx, templ_7745c5c3_Var2), templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return templ_7745c5c3_Err
	})
}

var _ = templruntime.GeneratedTemplate
