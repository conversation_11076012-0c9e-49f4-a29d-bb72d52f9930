// Code generated by templ - DO NOT EDIT.

// templ: version: v0.2.778
// templates/pages/farmers.templ

package pages

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/templates/layout"
	"strconv"
)

func Farmers(cooperatives []database.Cooperative) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Var2 := templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
			templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
			templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
			if !templ_7745c5c3_IsBuffer {
				defer func() {
					templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err == nil {
						templ_7745c5c3_Err = templ_7745c5c3_BufErr
					}
				}()
			}
			ctx = templ.InitializeContext(ctx)
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<div class=\"container mx-auto px-4 py-8\"><h1 class=\"text-4xl font-bold mb-8 text-gray-800\">Farmers</h1><div class=\"bg-white rounded-lg shadow-md p-6 mb-8\"><h2 class=\"text-2xl font-semibold mb-4 text-gray-700\">Select Cooperative</h2><select id=\"cooperative-select\" class=\"w-full p-2 border border-gray-300 rounded-md\" hx-get=\"/api/cooperatives/list-farmers\" hx-target=\"#farmer-list\" hx-trigger=\"change\" hx-include=\"[name=&#39;cooperative_id&#39;]\"><option value=\"\">Select a cooperative</option> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			for _, coop := range cooperatives {
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<option value=\"")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var3 string
				templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(strconv.Itoa(int(coop.ID)))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `templates/pages/farmers.templ`, Line: 24, Col: 65}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var4 string
				templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(coop.Name)
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `templates/pages/farmers.templ`, Line: 24, Col: 77}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("</option>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("</select> <input type=\"hidden\" name=\"cooperative_id\" value=\"\"></div><div id=\"farmer-list\" class=\"bg-white rounded-lg shadow-md p-6\"><h2 class=\"text-2xl font-semibold mb-4 text-gray-700\">Farmer List</h2><p class=\"text-gray-600\">Please select a cooperative to view farmers.</p></div></div><script>\n            document.getElementById('cooperative-select').addEventListener('change', function() {\n                document.querySelector('[name=\"cooperative_id\"]').value = this.value;\n            });\n\n            htmx.on('#farmer-list', 'htmx:afterSwap', function(evt) {\n                const farmerList = evt.detail.elt;\n                const farmers = JSON.parse(farmerList.textContent);\n                \n                if (farmers.length === 0) {\n                    farmerList.innerHTML = '<p class=\"text-gray-600\">No farmers found for this cooperative.</p>';\n                } else {\n                    let tableHTML = `\n                        <table class=\"min-w-full divide-y divide-gray-200 mt-4\">\n                            <thead class=\"bg-gray-50\">\n                                <tr>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name</th>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">SNO</th>\n                                    <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Phone Number</th>\n                                 \n                                </tr>\n                            </thead>\n                            <tbody>\n                    `;\n\n                    farmers.forEach(farmer => {\n                        tableHTML += `\n                            <tr>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">${farmer.name}</td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">${farmer.sno}</td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">${farmer.mobile_number}</td>\n         \n                            </tr>\n                        `;\n                    });\n\n                    tableHTML += `\n                            </tbody>\n                        </table>\n                    `;\n\n                    farmerList.innerHTML = tableHTML;\n                }\n            });\n        </script>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			return templ_7745c5c3_Err
		})
		templ_7745c5c3_Err = layout.Base("Farmers").Render(templ.WithChildren(ctx, templ_7745c5c3_Var2), templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return templ_7745c5c3_Err
	})
}

var _ = templruntime.GeneratedTemplate
