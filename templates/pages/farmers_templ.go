// Code generated by templ - DO NOT EDIT.

// templ: version: v0.2.778
// templates/pages/farmers.templ

package pages

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"github.com/itunza/africascongress/internal/database"
	"github.com/itunza/africascongress/templates/layout"
	"strconv"
)

func Farmers(cooperatives []database.Cooperative) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Var2 := templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
			templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
			templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
			if !templ_7745c5c3_IsBuffer {
				defer func() {
					templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
					if templ_7745c5c3_Err == nil {
						templ_7745c5c3_Err = templ_7745c5c3_BufErr
					}
				}()
			}
			ctx = templ.InitializeContext(ctx)
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<div class=\"container mx-auto px-4 py-8\"><!-- Header Section --><div class=\"flex justify-between items-center mb-8\"><h1 class=\"text-4xl font-bold text-gray-800\">Farmers Management</h1><div class=\"flex space-x-3\"><button id=\"refresh-btn\" class=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\"><svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path></svg> <span>Refresh</span></button> <button id=\"export-btn\" class=\"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\"><svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path></svg> <span>Export</span></button></div></div><!-- Cooperative Selection & Search Section --><div class=\"bg-white rounded-lg shadow-md p-6 mb-6\"><div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\"><!-- Cooperative Selection --><div><label for=\"cooperative-select\" class=\"block text-sm font-medium text-gray-700 mb-2\">Select Cooperative</label> <select id=\"cooperative-select\" class=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\" hx-get=\"/api/cooperatives/list-farmers\" hx-target=\"#farmer-list\" hx-trigger=\"change\" hx-include=\"[name=&#39;cooperative_id&#39;]\"><option value=\"\">Select a cooperative</option> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			for _, coop := range cooperatives {
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("<option value=\"")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var3 string
				templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(strconv.Itoa(int(coop.ID)))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `templates/pages/farmers.templ`, Line: 45, Col: 73}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var4 string
				templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(coop.Name)
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `templates/pages/farmers.templ`, Line: 45, Col: 85}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("</option>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString("</select> <input type=\"hidden\" name=\"cooperative_id\" value=\"\"></div><!-- Search Section --><div><label for=\"search-input\" class=\"block text-sm font-medium text-gray-700 mb-2\">Search Farmers</label><div class=\"relative\"><input type=\"text\" id=\"search-input\" name=\"q\" placeholder=\"Search by name, SNO, or phone...\" class=\"w-full p-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\" hx-get=\"/api/cooperatives/search-farmers\" hx-target=\"#farmer-list\" hx-trigger=\"keyup changed delay:300ms\" hx-include=\"[name=&#39;cooperative_id&#39;], [name=&#39;q&#39;]\" hx-indicator=\"#search-loading\"> <svg class=\"absolute left-3 top-3.5 h-4 w-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path></svg><!-- Loading indicator --><div id=\"search-loading\" class=\"absolute right-3 top-3.5 htmx-indicator\"><svg class=\"animate-spin h-4 w-4 text-blue-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\"><circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle> <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path></svg></div><!-- Search status indicator --><div id=\"search-status\" class=\"absolute right-10 top-3.5 hidden\"><div class=\"flex items-center space-x-1 text-xs text-gray-500\"><span id=\"search-count\">0</span> <span>results</span></div></div></div></div></div><!-- Action Buttons Row --><div class=\"mt-6 flex flex-wrap gap-3\"><button id=\"add-farmer-btn\" class=\"bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\" disabled><svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path></svg> <span>Add Farmer</span></button> <button id=\"bulk-import-btn\" class=\"bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\" disabled><svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path></svg> <span>Bulk Import</span></button> <button id=\"sync-farmers-btn\" class=\"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\" disabled><svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path></svg> <span>Sync with ERP</span></button></div></div><!-- Farmer List Section --><div id=\"farmer-list\" class=\"bg-white rounded-lg shadow-md\"><div class=\"p-6\"><div class=\"flex justify-between items-center mb-4\"><h2 class=\"text-xl font-semibold text-gray-700\">Farmer List</h2><div id=\"farmer-stats\" class=\"text-sm text-gray-500\"></div></div><div class=\"text-center py-12\"><svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path></svg><h3 class=\"mt-2 text-sm font-medium text-gray-900\">No farmers to display</h3><p class=\"mt-1 text-sm text-gray-500\">Please select a cooperative to view farmers.</p></div></div></div></div><script>\n            let currentCooperativeId = '';\n            let allFarmers = [];\n\n            // Initialize page functionality\n            document.addEventListener('DOMContentLoaded', function() {\n                initializeEventListeners();\n            });\n\n            function initializeEventListeners() {\n                // Cooperative selection handler\n                document.getElementById('cooperative-select').addEventListener('change', function() {\n                    currentCooperativeId = this.value;\n                    document.querySelector('[name=\"cooperative_id\"]').value = this.value;\n\n                    // Enable/disable action buttons based on selection\n                    toggleActionButtons(!!this.value);\n\n                    // Clear search when changing cooperative\n                    document.getElementById('search-input').value = '';\n                });\n\n                // Action button handlers\n                document.getElementById('refresh-btn').addEventListener('click', refreshFarmers);\n                document.getElementById('export-btn').addEventListener('click', exportFarmers);\n                document.getElementById('add-farmer-btn').addEventListener('click', showAddFarmerModal);\n                document.getElementById('bulk-import-btn').addEventListener('click', showBulkImportModal);\n                document.getElementById('sync-farmers-btn').addEventListener('click', syncFarmers);\n            }\n\n            function toggleActionButtons(enabled) {\n                const buttons = ['add-farmer-btn', 'bulk-import-btn', 'sync-farmers-btn'];\n                buttons.forEach(btnId => {\n                    const btn = document.getElementById(btnId);\n                    btn.disabled = !enabled;\n                    if (enabled) {\n                        btn.classList.remove('opacity-50', 'cursor-not-allowed');\n                    } else {\n                        btn.classList.add('opacity-50', 'cursor-not-allowed');\n                    }\n                });\n            }\n\n            function refreshFarmers() {\n                if (currentCooperativeId) {\n                    htmx.trigger('#cooperative-select', 'change');\n                    showNotification('Farmers list refreshed', 'success');\n                }\n            }\n\n            function exportFarmers() {\n                if (!currentCooperativeId) {\n                    showNotification('Please select a cooperative first', 'error');\n                    return;\n                }\n\n                // Create CSV content\n                const csvContent = generateCSV(allFarmers);\n                downloadCSV(csvContent, `farmers_${currentCooperativeId}_${new Date().toISOString().split('T')[0]}.csv`);\n                showNotification('Farmers data exported successfully', 'success');\n            }\n\n            function generateCSV(farmers) {\n                const headers = ['Name', 'SNO', 'Phone Number', 'Location', 'Gender', 'Bank', 'Account Number'];\n                const csvRows = [headers.join(',')];\n\n                farmers.forEach(farmer => {\n                    const row = [\n                        `\"${farmer.name || ''}\"`,\n                        `\"${farmer.sno || ''}\"`,\n                        `\"${farmer.mobile_number || ''}\"`,\n                        `\"${farmer.location || ''}\"`,\n                        `\"${farmer.gender || ''}\"`,\n                        `\"${farmer.bank || ''}\"`,\n                        `\"${farmer.account_number || ''}\"`\n                    ];\n                    csvRows.push(row.join(','));\n                });\n\n                return csvRows.join('\\n');\n            }\n\n            function downloadCSV(content, filename) {\n                const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });\n                const link = document.createElement('a');\n                if (link.download !== undefined) {\n                    const url = URL.createObjectURL(blob);\n                    link.setAttribute('href', url);\n                    link.setAttribute('download', filename);\n                    link.style.visibility = 'hidden';\n                    document.body.appendChild(link);\n                    link.click();\n                    document.body.removeChild(link);\n                }\n            }\n\n            function showAddFarmerModal() {\n                showNotification('Add farmer functionality coming soon', 'info');\n            }\n\n            function showBulkImportModal() {\n                showNotification('Bulk import functionality coming soon', 'info');\n            }\n\n            function syncFarmers() {\n                if (!currentCooperativeId) {\n                    showNotification('Please select a cooperative first', 'error');\n                    return;\n                }\n\n                showNotification('Syncing farmers with ERP...', 'info');\n\n                fetch(`/api/cooperatives/sync-farmers?cooperative_id=${currentCooperativeId}`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                    }\n                })\n                .then(response => response.json())\n                .then(data => {\n                    showNotification('Farmers synced successfully', 'success');\n                    refreshFarmers();\n                })\n                .catch(error => {\n                    showNotification('Error syncing farmers: ' + error.message, 'error');\n                });\n            }\n\n            function showNotification(message, type = 'info') {\n                // Create notification element\n                const notification = document.createElement('div');\n                notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${getNotificationClasses(type)}`;\n                notification.textContent = message;\n\n                document.body.appendChild(notification);\n\n                // Auto remove after 3 seconds\n                setTimeout(() => {\n                    notification.classList.add('opacity-0', 'translate-x-full');\n                    setTimeout(() => {\n                        document.body.removeChild(notification);\n                    }, 300);\n                }, 3000);\n            }\n\n            function getNotificationClasses(type) {\n                switch (type) {\n                    case 'success': return 'bg-green-500 text-white';\n                    case 'error': return 'bg-red-500 text-white';\n                    case 'warning': return 'bg-yellow-500 text-white';\n                    default: return 'bg-blue-500 text-white';\n                }\n            }\n\n            // HTMX event handlers\n            htmx.on('#farmer-list', 'htmx:afterSwap', function(evt) {\n                console.log('HTMX afterSwap triggered');\n                const farmerList = evt.detail.elt;\n                let farmers = [];\n\n                try {\n                    console.log('Raw response:', farmerList.textContent);\n                    farmers = JSON.parse(farmerList.textContent);\n                    allFarmers = farmers; // Store for export functionality\n                    console.log('Parsed farmers:', farmers);\n                } catch (e) {\n                    console.error('Error parsing farmer data:', e);\n                    console.log('Raw content that failed to parse:', farmerList.textContent);\n                    return;\n                }\n\n                updateFarmerStats(farmers.length);\n                updateSearchStatus(farmers.length);\n\n                if (farmers.length === 0) {\n                    renderEmptyState();\n                } else {\n                    renderFarmerTable(farmers);\n                }\n            });\n\n            // Add search-specific HTMX handlers\n            htmx.on('#search-input', 'htmx:beforeRequest', function(evt) {\n                console.log('Search request starting');\n                showSearchStatus(true);\n            });\n\n            htmx.on('#search-input', 'htmx:afterRequest', function(evt) {\n                console.log('Search request completed');\n                showSearchStatus(false);\n            });\n\n            // Add HTMX error handling\n            htmx.on('htmx:responseError', function(evt) {\n                console.error('HTMX Response Error:', evt.detail);\n                showNotification('Error loading data: ' + evt.detail.xhr.status, 'error');\n                showSearchStatus(false);\n            });\n\n            htmx.on('htmx:sendError', function(evt) {\n                console.error('HTMX Send Error:', evt.detail);\n                showNotification('Network error occurred', 'error');\n                showSearchStatus(false);\n            });\n\n            function updateFarmerStats(count) {\n                const statsElement = document.getElementById('farmer-stats');\n                if (statsElement) {\n                    statsElement.textContent = `Total: ${count} farmer${count !== 1 ? 's' : ''}`;\n                }\n            }\n\n            function updateSearchStatus(count) {\n                const searchQuery = document.getElementById('search-input').value.trim();\n                const statusElement = document.getElementById('search-status');\n                const countElement = document.getElementById('search-count');\n\n                if (searchQuery && statusElement && countElement) {\n                    countElement.textContent = count;\n                    statusElement.classList.remove('hidden');\n                } else if (statusElement) {\n                    statusElement.classList.add('hidden');\n                }\n            }\n\n            function showSearchStatus(isSearching) {\n                const loadingElement = document.getElementById('search-loading');\n                const statusElement = document.getElementById('search-status');\n\n                if (isSearching) {\n                    if (statusElement) statusElement.classList.add('hidden');\n                } else {\n                    // Status will be updated by updateSearchStatus\n                }\n            }\n\n            function renderEmptyState() {\n                const farmerList = document.getElementById('farmer-list');\n                const searchQuery = document.getElementById('search-input').value.toLowerCase();\n                const isSearching = searchQuery.length > 0;\n\n                farmerList.innerHTML = `\n                    <div class=\"p-6\">\n                        <div class=\"flex justify-between items-center mb-6\">\n                            <div>\n                                <h2 class=\"text-xl font-semibold text-gray-700\">Farmer List</h2>\n                                ${isSearching ? `<p class=\"text-sm text-gray-500 mt-1\">Search results for: \"<span class=\"font-medium text-gray-700\">${searchQuery}</span>\"</p>` : ''}\n                            </div>\n                            <div class=\"text-right\">\n                                <div id=\"farmer-stats\" class=\"text-sm text-gray-500\">Total: 0 farmers</div>\n                                ${isSearching ? `<button onclick=\"clearSearch()\" class=\"text-xs text-blue-600 hover:text-blue-800 mt-1\">Clear search</button>` : ''}\n                            </div>\n                        </div>\n                        <div class=\"text-center py-16\">\n                            ${isSearching ? `\n                                <svg class=\"mx-auto h-16 w-16 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n                                </svg>\n                                <h3 class=\"mt-4 text-lg font-medium text-gray-900\">No farmers match your search</h3>\n                                <p class=\"mt-2 text-sm text-gray-500\">\n                                    We couldn't find any farmers matching \"<span class=\"font-medium\">${searchQuery}</span>\".\n                                    <br>Try adjusting your search terms or browse all farmers.\n                                </p>\n                                <div class=\"mt-6\">\n                                    <button onclick=\"clearSearch()\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\">\n                                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                                        </svg>\n                                        Clear search\n                                    </button>\n                                </div>\n                            ` : `\n                                <svg class=\"mx-auto h-16 w-16 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                                </svg>\n                                <h3 class=\"mt-4 text-lg font-medium text-gray-900\">No farmers found</h3>\n                                <p class=\"mt-2 text-sm text-gray-500\">\n                                    This cooperative doesn't have any farmers yet.\n                                    <br>Add farmers to get started.\n                                </p>\n                                <div class=\"mt-6\">\n                                    <button onclick=\"showAddFarmerModal()\" class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\">\n                                        <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                                        </svg>\n                                        Add first farmer\n                                    </button>\n                                </div>\n                            `}\n                        </div>\n                    </div>\n                `;\n            }\n\n            function renderFarmerTable(farmers) {\n                const farmerList = document.getElementById('farmer-list');\n                const searchQuery = document.getElementById('search-input').value.toLowerCase();\n\n                let tableHTML = `\n                    <div class=\"p-6\">\n                        <div class=\"flex justify-between items-center mb-6\">\n                            <div>\n                                <h2 class=\"text-xl font-semibold text-gray-700\">Farmer List</h2>\n                                ${searchQuery ? `<p class=\"text-sm text-gray-500 mt-1\">Search results for: \"<span class=\"font-medium text-gray-700\">${searchQuery}</span>\"</p>` : ''}\n                            </div>\n                            <div class=\"text-right\">\n                                <div id=\"farmer-stats\" class=\"text-sm text-gray-500\">Total: ${farmers.length} farmer${farmers.length !== 1 ? 's' : ''}</div>\n                                ${searchQuery ? `<button onclick=\"clearSearch()\" class=\"text-xs text-blue-600 hover:text-blue-800 mt-1\">Clear search</button>` : ''}\n                            </div>\n                        </div>\n                        <div class=\"overflow-x-auto shadow ring-1 ring-black ring-opacity-5 md:rounded-lg\">\n                            <table class=\"min-w-full divide-y divide-gray-200\">\n                                <thead class=\"bg-gray-50\">\n                                    <tr>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                            <div class=\"flex items-center space-x-1\">\n                                                <span>Farmer Details</span>\n                                                <svg class=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"></path>\n                                                </svg>\n                                            </div>\n                                        </th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                            <div class=\"flex items-center space-x-1\">\n                                                <span>Contact Info</span>\n                                                <svg class=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"></path>\n                                                </svg>\n                                            </div>\n                                        </th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                            <div class=\"flex items-center space-x-1\">\n                                                <span>Additional Info</span>\n                                                <svg class=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                                                </svg>\n                                            </div>\n                                        </th>\n                                        <th scope=\"col\" class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                                            <div class=\"flex items-center space-x-1\">\n                                                <span>Actions</span>\n                                                <svg class=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z\"></path>\n                                                </svg>\n                                            </div>\n                                        </th>\n                                    </tr>\n                                </thead>\n                                <tbody class=\"bg-white divide-y divide-gray-200\">\n                `;\n\n                farmers.forEach((farmer, index) => {\n                    const rowClass = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';\n\n                    // Highlight search terms\n                    const highlightedName = highlightSearchTerm(farmer.name || 'N/A', searchQuery);\n                    const highlightedSno = highlightSearchTerm(farmer.sno || 'N/A', searchQuery);\n                    const highlightedPhone = highlightSearchTerm(farmer.mobile_number || 'N/A', searchQuery);\n\n                    // Format additional info\n                    const location = farmer.location || 'Not specified';\n                    const gender = farmer.gender || 'Not specified';\n                    const bank = farmer.bank || 'Not specified';\n                    const accountNumber = farmer.account_number || 'Not specified';\n\n                    tableHTML += `\n                        <tr class=\"${rowClass} hover:bg-blue-50 transition-colors duration-150 ease-in-out\">\n                            <td class=\"px-6 py-4\">\n                                <div class=\"flex items-center\">\n                                    <div class=\"flex-shrink-0 h-10 w-10\">\n                                        <div class=\"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center\">\n                                            <span class=\"text-sm font-medium text-white\">${getInitials(farmer.name)}</span>\n                                        </div>\n                                    </div>\n                                    <div class=\"ml-4\">\n                                        <div class=\"text-sm font-medium text-gray-900\">${highlightedName}</div>\n                                        <div class=\"text-sm text-gray-500\">SNO: ${highlightedSno}</div>\n                                        <div class=\"text-xs text-gray-400\">ID: ${farmer.id}</div>\n                                    </div>\n                                </div>\n                            </td>\n                            <td class=\"px-6 py-4\">\n                                <div class=\"text-sm text-gray-900\">\n                                    <div class=\"flex items-center space-x-2\">\n                                        <svg class=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"></path>\n                                        </svg>\n                                        <span>${highlightedPhone}</span>\n                                    </div>\n                                </div>\n                                <div class=\"text-sm text-gray-500 mt-1\">\n                                    <div class=\"flex items-center space-x-2\">\n                                        <svg class=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"></path>\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n                                        </svg>\n                                        <span>${location}</span>\n                                    </div>\n                                </div>\n                            </td>\n                            <td class=\"px-6 py-4\">\n                                <div class=\"space-y-2\">\n                                    <div class=\"flex items-center space-x-2\">\n                                        <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getGenderBadgeClass(farmer.gender)}\">\n                                            ${gender}\n                                        </span>\n                                    </div>\n                                    <div class=\"text-xs text-gray-500\">\n                                        <div class=\"flex items-center space-x-1\">\n                                            <svg class=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"></path>\n                                            </svg>\n                                            <span>${bank}</span>\n                                        </div>\n                                        ${accountNumber !== 'Not specified' ? `<div class=\"mt-1\">Acc: ${accountNumber}</div>` : ''}\n                                    </div>\n                                </div>\n                            </td>\n                            <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                <div class=\"flex items-center space-x-2\">\n                                    <button onclick=\"editFarmer(${farmer.id})\"\n                                            class=\"inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150\">\n                                        <svg class=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                                        </svg>\n                                        Edit\n                                    </button>\n                                    <button onclick=\"deleteFarmer(${farmer.id})\"\n                                            class=\"inline-flex items-center px-3 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150\">\n                                        <svg class=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n                                        </svg>\n                                        Delete\n                                    </button>\n                                </div>\n                            </td>\n                        </tr>\n                    `;\n                });\n\n                tableHTML += `\n                                </tbody>\n                            </table>\n                        </div>\n                    </div>\n                `;\n\n                farmerList.innerHTML = tableHTML;\n            }\n\n            function getGenderBadgeClass(gender) {\n                switch (gender?.toLowerCase()) {\n                    case 'male': return 'bg-blue-100 text-blue-800';\n                    case 'female': return 'bg-pink-100 text-pink-800';\n                    default: return 'bg-gray-100 text-gray-800';\n                }\n            }\n\n            function highlightSearchTerm(text, searchTerm) {\n                if (!searchTerm || searchTerm.length < 2) return text;\n\n                const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');\n                return text.replace(regex, '<mark class=\"bg-yellow-200 text-yellow-900 px-1 rounded\">$1</mark>');\n            }\n\n            function escapeRegExp(string) {\n                return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n            }\n\n            function getInitials(name) {\n                if (!name) return '?';\n                return name.split(' ')\n                    .map(word => word.charAt(0))\n                    .join('')\n                    .toUpperCase()\n                    .substring(0, 2);\n            }\n\n            function clearSearch() {\n                document.getElementById('search-input').value = '';\n                if (currentCooperativeId) {\n                    htmx.trigger('#cooperative-select', 'change');\n                }\n            }\n\n            function editFarmer(farmerId) {\n                showNotification(`Edit farmer ${farmerId} - Coming soon`, 'info');\n            }\n\n            function deleteFarmer(farmerId) {\n                if (confirm('Are you sure you want to delete this farmer?')) {\n                    showNotification(`Delete farmer ${farmerId} - Coming soon`, 'info');\n                }\n            }\n        </script>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			return templ_7745c5c3_Err
		})
		templ_7745c5c3_Err = layout.Base("Farmers").Render(templ.WithChildren(ctx, templ_7745c5c3_Var2), templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return templ_7745c5c3_Err
	})
}

var _ = templruntime.GeneratedTemplate
