package rates

import (
	"github.com/itunza/africascongress/internal/database"
	"fmt"
	"strconv"
	"github.com/jackc/pgx/v5/pgtype"
)

func formatNumericForInput(n pgtype.Numeric) string {
	if !n.Valid {
		return ""
	}

	// Try to get the string representation and parse it
	if val, err := n.Value(); err == nil {
		if str, ok := val.(string); ok {
			// Try to parse as float and format to 2 decimal places
			if f, err := strconv.ParseFloat(str, 64); err == nil {
				return fmt.Sprintf("%.2f", f)
			}
			// If parsing fails, return the string as-is
			return str
		}
		// If it's not a string, convert to string
		return fmt.Sprintf("%v", val)
	}

	return ""
}

templ EditForm(rate database.GetRateRow, agents []database.ListAllAgentsForCooperativeRow) {
	<tr id={ fmt.Sprintf("rate-row-%d", rate.ID) }>
		<td colspan="7" class="px-6 py-4">
			<form
				id={ fmt.Sprintf("edit-form-%d", rate.ID) }
				hx-post={ fmt.Sprintf("/api/rates/update?id=%d", rate.ID) }
				hx-target={ fmt.Sprintf("#rate-row-%d", rate.ID) }
				hx-swap="outerHTML"
				class="grid grid-cols-7 gap-2 items-center"
			>
				<div>
					<label class="block text-xs font-medium text-gray-700 mb-1">Month</label>
					if rate.Month.Valid {
						<input
							type="date"
							name="month"
							value={ rate.Month.Time.Format("2006-01-02") }
							required
							class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
						>
					} else {
						<input
							type="date"
							name="month"
							required
							class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
						>
					}
				</div>

				<div>
					<label class="block text-xs font-medium text-gray-700 mb-1">Agent</label>
					<select
						name="agent_id"
						class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
					>
						<option value="">No agent assigned</option>
						for _, agent := range agents {
							if rate.AgentID != nil && *rate.AgentID == agent.ID {
								<option value={ fmt.Sprintf("%d", agent.ID) } selected>
									{ agent.Email }
								</option>
							} else {
								<option value={ fmt.Sprintf("%d", agent.ID) }>
									{ agent.Email }
								</option>
							}
						}
					</select>
				</div>

				<div>
					<label class="block text-xs font-medium text-gray-700 mb-1">Base Rate</label>
					<input
						type="number"
						step="0.01"
						name="base_rate"
						value={ formatNumericForInput(rate.BaseRate) }
						required
						class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
					>
				</div>

				<div>
					<label class="block text-xs font-medium text-gray-700 mb-1">Delivery Method</label>
					<select
						name="delivery_method"
						class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
					>
						<option value="">Select method</option>
						if rate.DeliveryMethod != nil && *rate.DeliveryMethod == "agent" {
							<option value="agent" selected>Agent</option>
						} else {
							<option value="agent">Agent</option>
						}
						if rate.DeliveryMethod != nil && *rate.DeliveryMethod == "chiller" {
							<option value="chiller" selected>Chiller</option>
						} else {
							<option value="chiller">Chiller</option>
						}
					</select>
				</div>

				<div>
					<label class="block text-xs font-medium text-gray-700 mb-1">Deduction</label>
					<input
						type="number"
						step="0.01"
						name="deduction"
						value={ formatNumericForInput(rate.Deduction) }
						class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
					>
				</div>

				<div>
					<label class="block text-xs font-medium text-gray-700 mb-1">Final Rate</label>
					<span class="text-gray-500 text-sm">Auto-calculated</span>
				</div>

				<div class="flex gap-2">
					<button
						type="submit"
						class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
					>
						Save
					</button>
					<button
						type="button"
						class="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
						hx-get={ fmt.Sprintf("/api/rates/cancel-edit?id=%d", rate.ID) }
						hx-target={ fmt.Sprintf("#rate-row-%d", rate.ID) }
						hx-swap="outerHTML"
					>
						Cancel
					</button>
				</div>
			</form>
		</td>
	</tr>
}
