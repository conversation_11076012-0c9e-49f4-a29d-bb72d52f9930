package rates

import (
	"github.com/itunza/africascongress/internal/database"
	"fmt"
	"strconv"
	"github.com/jackc/pgx/v5/pgtype"
)

func formatNumeric(n pgtype.Numeric) string {
	if !n.Valid {
		return "0.00"
	}

	// Try to get the string representation and parse it
	if val, err := n.Value(); err == nil {
		if str, ok := val.(string); ok {
			// Try to parse as float and format to 2 decimal places
			if f, err := strconv.ParseFloat(str, 64); err == nil {
				return fmt.Sprintf("%.2f", f)
			}
			// If parsing fails, return the string as-is
			return str
		}
		// If it's not a string, convert to string
		return fmt.Sprintf("%v", val)
	}

	return "0.00"
}

templ List(rates []database.ListRatesByCooperativeRow) {
	<div id="rates-list" class="mt-4">
		if len(rates) == 0 {
			<div class="text-center py-8 text-gray-500">
				<p>No rates found for this cooperative.</p>
			</div>
		} else {
			<div class="overflow-x-auto">
				<table class="min-w-full bg-white border border-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Base Rate</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery Method</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deduction</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final Rate</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
						</tr>
					</thead>
					<tbody class="bg-white divide-y divide-gray-200">
						for _, rate := range rates {
							<tr id={ fmt.Sprintf("rate-row-%d", rate.ID) }>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									if rate.Month.Valid {
										{ rate.Month.Time.Format("2006-01-02") }
									} else {
										-
									}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									if rate.AgentEmail != nil {
										{ *rate.AgentEmail }
									} else {
										<span class="text-gray-400">No agent assigned</span>
									}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									{ formatNumeric(rate.BaseRate) }
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									if rate.DeliveryMethod != nil {
										{ *rate.DeliveryMethod }
									} else {
										-
									}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
									{ formatNumeric(rate.Deduction) }
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
									{ formatNumeric(rate.FinalRate) }
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
									<button
										class="text-indigo-600 hover:text-indigo-900 mr-3"
										hx-get={ fmt.Sprintf("/api/rates/edit?id=%d", rate.ID) }
										hx-target={ fmt.Sprintf("#rate-row-%d", rate.ID) }
										hx-swap="outerHTML"
									>
										Edit
									</button>
									<button
										class="text-red-600 hover:text-red-900"
										hx-delete={ fmt.Sprintf("/api/rates/delete?id=%d", rate.ID) }
										hx-target="#rates-list"
										hx-confirm="Are you sure you want to delete this rate?"
									>
										Delete
									</button>
								</td>
							</tr>
						}
					</tbody>
				</table>
			</div>
		}
	</div>
}
