package rates

import (
	"github.com/itunza/africascongress/internal/database"
	"fmt"
)

templ AgentSelect(agents []database.ListAllAgentsForCooperativeRow) {
	<select name="agent_id" id="agent_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
		<option value="">Select an agent (optional)</option>
		for _, agent := range agents {
			<option value={ fmt.Sprintf("%d", agent.ID) }>
				{ agent.Email } - { agent.MobileNumber }
			</option>
		}
	</select>
}
